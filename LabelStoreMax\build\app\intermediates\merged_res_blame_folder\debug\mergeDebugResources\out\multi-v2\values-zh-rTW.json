{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "95,104,185,189,518,524,525", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8711,9382,15438,15707,40185,40773,40852", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "8772,9458,15503,15822,40348,40847,40923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6794,6895,7023,7138,7240,7347,7463,7563,7761,7871,7972,8101,8216,8318,8426,8482,8539", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "6890,7018,7133,7235,7342,7458,7558,7653,7866,7967,8096,8211,8313,8421,8477,8534,8608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,355,467,511,569,644,708,824,879,951,1007,1061,1125,1190,1270,1414,1474,1534,1585,1636,1702,1786,1863,1918,1990,2058,2125,2185,2260,2413,2479,2551,2605,2663,2722,2780,2841,2907,2997,3074,3151,3242,3326,3396,3475,3560,3661,3770,3862,3956,4005,4241,4316,4373", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,350,462,506,564,639,703,819,874,946,1002,1056,1120,1185,1265,1409,1469,1529,1580,1631,1697,1781,1858,1913,1985,2053,2120,2180,2255,2408,2474,2546,2600,2658,2717,2775,2836,2902,2992,3069,3146,3237,3321,3391,3470,3555,3656,3765,3857,3951,4000,4236,4311,4368,4423"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,354,355,356,357,358,364,367,372,374,375,376,377,380,382,383,387,391,392,394,396,408,433,434,435,436,437,455,462,463,464,465,467,468,469,486", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22040,22129,22264,22409,22971,23015,23073,23148,23212,23328,23383,24732,24923,25055,25316,25381,25717,26162,26222,26681,27340,27514,27580,27664,27741,27796,28220,28411,28750,28868,28943,29096,29162,29359,29485,29543,29809,30202,30263,30414,30586,31837,33724,33815,33899,33969,34048,35443,35906,36015,36107,36201,36306,36542,36617,37917", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "22124,22259,22335,22516,23010,23068,23143,23207,23323,23378,23450,24783,24972,25114,25376,25456,25856,26217,26277,26727,27386,27575,27659,27736,27791,27863,28283,28473,28805,28938,29091,29157,29229,29408,29538,29597,29862,30258,30324,30499,30658,31909,33810,33894,33964,34043,34128,35539,36010,36102,36196,36245,36537,36612,36669,37967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3215,3293,3370,3436,3514,3591,3676,3797,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5487,5545,5622,5706,5807,5874,5937,6017,6067,6118,6176,6245,6363,6528,6711,6790,6850,6915,6974,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3210,3288,3365,3431,3509,3586,3671,3792,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5482,5540,5617,5701,5802,5869,5932,6012,6062,6113,6171,6240,6358,6523,6706,6785,6845,6910,6969,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,361,379,381,384,388,389,390,393,395,397,398,399,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,460,470,471,472,473,474,475,476,477,478,487,488,489,490,491,492,493,494,495,496,497,498,499,500,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17348,17407,17473,22340,23455,23526,23599,24977,25906,26019,26090,28007,29308,29413,29602,29867,29985,30107,30329,30504,30663,30747,30833,30937,31025,31111,31230,31315,31414,31582,31749,31914,32004,32106,32185,32242,32301,32375,32453,32530,32596,32674,32751,32836,32957,33021,33083,33144,33234,33304,33377,33436,33505,33573,33636,34133,34214,34305,34386,34451,34522,34586,34657,34713,34796,34872,34956,35056,35114,35191,35275,35376,35544,35776,36674,36724,36775,36833,36902,37020,37185,37368,37447,37972,38037,38096,38176,38261,38334,38402,38467,38536,38599,38674,38752,38836,38901,39026,39076,39189,39254,39307,39375,39437,39515,39581,39666,39752,39830,39901,39968,40031,40091", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "17402,17468,17529,22404,23521,23594,23667,25050,25970,26085,26157,28081,29354,29480,29656,29980,30102,30197,30409,30581,30742,30828,30932,31020,31106,31225,31310,31409,31577,31744,31832,31999,32101,32180,32237,32296,32370,32448,32525,32591,32669,32746,32831,32952,33016,33078,33139,33229,33299,33372,33431,33500,33568,33631,33719,34209,34300,34381,34446,34517,34581,34652,34708,34791,34867,34951,35051,35109,35186,35270,35371,35438,35602,35851,36719,36770,36828,36897,37015,37180,37363,37442,37502,38032,38091,38171,38256,38329,38397,38462,38531,38594,38669,38747,38831,38896,38959,39071,39184,39249,39302,39370,39432,39510,39576,39661,39747,39825,39896,39963,40026,40086,40180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4355,4432,8861,9030,9191,11034,11108,15285,15363,15508,15573,15827,15900,16202,40353,40427,40495", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "4427,4503,8943,9116,9264,11103,11180,15358,15433,15568,15633,15895,15970,16265,40422,40490,40606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "8777,9612,9704,9805", "endColumns": "83,91,100,92", "endOffsets": "8856,9699,9800,9893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "522,523", "startColumns": "4,4", "startOffsets": "40611,40694", "endColumns": "82,78", "endOffsets": "40689,40768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "7658", "endColumns": "102", "endOffsets": "7756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "125,209,274,340,400,462,523"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16441,16516,16600,16665,16731,16791,16853", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "16511,16595,16660,16726,16786,16848,16909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1333,1406,1471,1542,1614,1677,1722,1768,1830,1892,1945,2007,2079,2146,2216", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1328,1401,1466,1537,1609,1672,1717,1763,1825,1887,1940,2002,2074,2141,2211,2280"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,359,360,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17781,17996,18224,18357,18430,18495,18945,19008,19131,19191,19259,19323,19384,19442,19508,19627,19692,19897,20671,20730,21587,21727,21792,24788,24860,25861,26573,26619,26732,27225,27278,27868,27940,28478,28548", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "17836,18062,18283,18425,18490,18551,19003,19064,19186,19254,19318,19379,19437,19503,19565,19687,19745,19959,20725,20797,21655,21787,21858,24855,24918,25901,26614,26676,26789,27273,27335,27935,28002,28543,28612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,15975", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,16049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,287,398,507,587,669,745,830,915,1022,1126,1212,1298,1392,1501,1579,1658,1781,1874,1969,2075,2169", "endColumns": "130,100,110,108,79,81,75,84,84,106,103,85,85,93,108,77,78,122,92,94,105,93,99", "endOffsets": "181,282,393,502,582,664,740,825,910,1017,1121,1207,1293,1387,1496,1574,1653,1776,1869,1964,2070,2164,2264"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4580,4711,4812,4923,5032,5112,5194,5270,5355,5440,5547,5651,5737,5823,5917,6026,6104,6183,6306,6399,6494,6600,6694", "endColumns": "130,100,110,108,79,81,75,84,84,106,103,85,85,93,108,77,78,122,92,94,105,93,99", "endOffsets": "4706,4807,4918,5027,5107,5189,5265,5350,5435,5542,5646,5732,5818,5912,6021,6099,6178,6301,6394,6489,6595,6689,6789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3420,3512,3611,3705,3799,3892,3985,16340", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3507,3606,3700,3794,3887,3980,4076,16436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "120,526", "startColumns": "4,4", "startOffsets": "10913,40928", "endColumns": "60,71", "endOffsets": "10969,40995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2863,2969", "endColumns": "105,110", "endOffsets": "2964,3075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,197", "endColumns": "71,69,70", "endOffsets": "122,192,263"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4508,9121,9463", "endColumns": "71,69,70", "endOffsets": "4575,9186,9529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,245,306,375,429,489,537,602,668,736,820,904,977,1046,1116,1187,1267,1346,1409,1485,1558,1633,1721,1791,1867,1937,2020,2085,2160,2233,2302,2371,2453,2513,2580,2672,2757,2820,2881,3207,3508,3573,3657,3737,3792,3868,3940,3998,4069,4142,4197,4267,4312,4382,4464,4521,4586,4653,4720,4764,4828,4905,4969,5012,5055,5110,5168,5226,5317,5409,5486,5546,5609,5668,5743,5806,5866,5928,5999,6057,6131,6202,6279,6328,6403,6448,6498,6554,6610,6671,6730,6791,6862,6919,6964", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "110,178,240,301,370,424,484,532,597,663,731,815,899,972,1041,1111,1182,1262,1341,1404,1480,1553,1628,1716,1786,1862,1932,2015,2080,2155,2228,2297,2366,2448,2508,2575,2667,2752,2815,2876,3202,3503,3568,3652,3732,3787,3863,3935,3993,4064,4137,4192,4262,4307,4377,4459,4516,4581,4648,4715,4759,4823,4900,4964,5007,5050,5105,5163,5221,5312,5404,5481,5541,5604,5663,5738,5801,5861,5923,5994,6052,6126,6197,6274,6323,6398,6443,6493,6549,6605,6666,6725,6786,6857,6914,6959,7021"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,362,363,365,366,370,371,373,378,385,386,457,458,459,461,466,479,480,481,482,483,484,485,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16914,16974,17042,17104,17165,17234,17288,17534,17582,17647,17713,17912,18067,18151,18288,18613,18874,19817,20030,20109,20172,20308,20381,20456,20601,20865,20941,21011,21094,21159,21234,21307,21376,21445,21527,21660,21863,21955,22521,22584,22645,23672,23973,24038,24122,24202,24257,24333,24405,24463,24534,24607,24662,25119,25164,25234,25461,25518,25583,25650,25975,26282,26346,26423,26487,26530,26794,26849,26907,26965,27056,27148,27391,27451,28086,28145,28288,28351,28617,28679,28810,29234,29661,29732,35607,35656,35731,35856,36250,37507,37563,37624,37683,37744,37815,37872,38964", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "16969,17037,17099,17160,17229,17283,17343,17577,17642,17708,17776,17991,18146,18219,18352,18678,18940,19892,20104,20167,20243,20376,20451,20539,20666,20936,21006,21089,21154,21229,21302,21371,21440,21522,21582,21722,21950,22035,22579,22640,22966,23968,24033,24117,24197,24252,24328,24400,24458,24529,24602,24657,24727,25159,25229,25311,25513,25578,25645,25712,26014,26341,26418,26482,26525,26568,26844,26902,26960,27051,27143,27220,27446,27509,28140,28215,28346,28406,28674,28745,28863,29303,29727,29804,35651,35726,35771,35901,36301,37558,37619,37678,37739,37810,37867,37912,39021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,235,334,437,540,638,739,843,933,1045,1152", "endColumns": "97,81,98,102,102,97,100,103,89,111,106,97", "endOffsets": "148,230,329,432,535,633,734,838,928,1040,1147,1245"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8613,8948,9898,9997,10100,10203,10301,10402,10506,10596,10708,10815", "endColumns": "97,81,98,102,102,97,100,103,89,111,106,97", "endOffsets": "8706,9025,9992,10095,10198,10296,10397,10501,10591,10703,10810,10908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17841,18556,18683,18743,18816,19069,19570,19750,19964,20248,20544,20802", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "17907,18608,18738,18811,18869,19126,19622,19812,20025,20303,20596,20860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3144,3206,3273,3343,4081,4175,4282,9269,9320,9534,10974,11185,11245,11323,11384,11442,11498,11558,11616,11670,11755,11811,11869,11923,11988,12080,12154,12226,12308,12382,12459,12579,12642,12705,12804,12881,12955,13005,13056,13122,13185,13253,13324,13395,13456,13527,13594,13656,13743,13822,13887,13970,14055,14129,14193,14269,14317,14390,14454,14530,14608,14670,14734,14797,14863,14943,15021,15097,15176,15230,15638,16054,16129,16270", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,3139,3201,3268,3338,3415,4170,4277,4350,9315,9377,9607,11029,11240,11318,11379,11437,11493,11553,11611,11665,11750,11806,11864,11918,11983,12075,12149,12221,12303,12377,12454,12574,12637,12700,12799,12876,12950,13000,13051,13117,13180,13248,13319,13390,13451,13522,13589,13651,13738,13817,13882,13965,14050,14124,14188,14264,14312,14385,14449,14525,14603,14665,14729,14792,14858,14938,15016,15092,15171,15225,15280,15702,16124,16197,16335"}}]}]}