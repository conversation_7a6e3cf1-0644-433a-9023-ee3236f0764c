//  Velvete Store
//
//  Created by we008.
//  2025, <PERSON><PERSON>. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/app/services/woocommerce_service.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/bootstrap/helpers.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/app_config.dart';

class ProductDetailRecommendedWidget extends StatefulWidget {
  const ProductDetailRecommendedWidget({
    super.key,
    required this.product,
    required this.appConfig,
  });

  final MyProduct? product;
  final AppConfig? appConfig;

  @override
  createState() => _ProductDetailRecommendedWidgetState();
}

class _ProductDetailRecommendedWidgetState extends State<ProductDetailRecommendedWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.product == null) {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'موصى لك',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.headlineSmall?.color,
              ),
            ),
          ),
          
          SizedBox(height: 12),
          
          // Horizontal Products List - Responsive height
          ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: DesignConstants.getResponsiveHeight(context,
                mobileRatio: 0.25,    // 25% of screen height on mobile
                tabletRatio: 0.22,    // 22% of screen height on tablet
                desktopRatio: 0.20,   // 20% of screen height on desktop
              ),
              maxHeight: DesignConstants.getResponsiveHeight(context,
                mobileRatio: 0.40,    // 40% of screen height on mobile
                tabletRatio: 0.35,    // 35% of screen height on tablet
                desktopRatio: 0.30,   // 30% of screen height on desktop
              ),
            ),
            child: FutureBuilder<List<MyProduct>>(
              future: _getRecommendedProducts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: ThemeColor.get(context).brandPrimary,
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      'خطأ في تحميل المنتجات الموصى بها',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  );
                }

                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات موصى بها حالياً',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  );
                }

                List<MyProduct> recommendedProducts = snapshot.data!;
                
                return ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  itemCount: recommendedProducts.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 160,
                      constraints: BoxConstraints(
                        minHeight: 200,
                        maxHeight: 320,
                      ),
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      child: ProductItemContainer(
                        product: recommendedProducts[index],
                        onTap: () => _showProduct(recommendedProducts[index]),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<List<MyProduct>> _getRecommendedProducts() async {
    if (widget.product == null) return [];

    try {
      print('🔍 Getting recommendations for product: ${widget.product!.name}');
      print('🔍 Product ID: ${widget.product!.id}');
      print('🔍 Product categories: ${widget.product!.categories?.map((c) => c.name).join(', ')}');
      print('🔍 Product price: ${widget.product!.price}');

      List<MyProduct> allRecommendations = [];
      Set<int> addedProductIds = {widget.product!.id}; // Exclude current product

      // 1. Get products from same category
      if (widget.product!.categories?.isNotEmpty == true) {
        for (var category in widget.product!.categories!) {
          try {
            // Get basic category products
            List<MyProduct> basicProducts = await WooCommerceService().getProducts(
              page: 1,
              perPage: 10,
              category: category.id,
              status: WooFilterStatus.publish,
              stockStatus: WooProductStockStatus.instock,
            );

            // Fetch complete data for each product to ensure pricing accuracy
            List<MyProduct> categoryProducts = [];
            for (MyProduct basicProduct in basicProducts) {
              try {
                MyProduct completeProduct = await WooCommerceService().getProduct(basicProduct.id);
                categoryProducts.add(completeProduct);
              } catch (e) {
                print('⚠️ Failed to fetch complete data for recommended product ${basicProduct.id}: $e');
                categoryProducts.add(basicProduct);
              }
            }

            // Filter out current product and already added products
            categoryProducts = categoryProducts
                .where((p) => !addedProductIds.contains(p.id))
                .toList();

            allRecommendations.addAll(categoryProducts);
            addedProductIds.addAll(categoryProducts.map((p) => p.id));

            print('✅ Found ${categoryProducts.length} products from category: ${category.name}');
          } catch (e) {
            print('❌ Error fetching products from category ${category.name}: $e');
          }
        }
      }

      // 2. Get featured products if we don't have enough recommendations
      if (allRecommendations.length < 6) {
        try {
          // Note: WooCommerce API doesn't have a direct 'featured' filter in the current implementation
          // We'll get recent products as a fallback
          List<MyProduct> featuredProducts = await WooCommerceService().getProducts(
            page: 1,
            perPage: 10,
            orderBy: WooSortOrderBy.popularity, // This might not work, fallback to date
            order: WooSortOrder.desc,
            status: WooFilterStatus.publish,
            stockStatus: WooProductStockStatus.instock,
          );

          // Filter out current product and already added products
          featuredProducts = featuredProducts
              .where((p) => !addedProductIds.contains(p.id))
              .toList();

          allRecommendations.addAll(featuredProducts);
          addedProductIds.addAll(featuredProducts.map((p) => p.id));

          print('✅ Found ${featuredProducts.length} featured/popular products');
        } catch (e) {
          print('❌ Error fetching featured products: $e');
        }
      }

      // 3. Price-based recommendations (within ±20% of current product price)
      // PHASE 11: Use robust getSafePrice() method
      if (allRecommendations.length < 8) {
        try {
          double currentPrice = widget.product!.getSafePrice().toDouble();
          if (currentPrice > 0) {
            double minPrice = currentPrice * 0.8; // -20%
            double maxPrice = currentPrice * 1.2; // +20%

            // Get more products to filter by price locally
            List<MyProduct> allProducts = await WooCommerceService().getProducts(
              page: 1,
              perPage: 30,
              status: WooFilterStatus.publish,
              stockStatus: WooProductStockStatus.instock,
            );

            // Filter by price range and exclude already added products
            // PHASE 11: Use robust getSafePrice() method
            List<MyProduct> priceBasedProducts = allProducts.where((p) {
              if (addedProductIds.contains(p.id)) return false;

              double productPrice = p.getSafePrice().toDouble();
              return productPrice >= minPrice && productPrice <= maxPrice;
            }).toList();

            allRecommendations.addAll(priceBasedProducts);
            print('✅ Found ${priceBasedProducts.length} products in similar price range (${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)} LYD)');
          }
        } catch (e) {
          print('❌ Error fetching price-based recommendations: $e');
        }
      }

      // Remove duplicates and limit to 8 products
      Map<int, MyProduct> uniqueProducts = {};
      for (var product in allRecommendations) {
        if (!uniqueProducts.containsKey(product.id) && product.id != widget.product!.id) {
          uniqueProducts[product.id] = product;
        }
      }

      List<MyProduct> finalRecommendations = uniqueProducts.values.take(8).toList();
      print('✅ Final recommendations: ${finalRecommendations.length} products');

      return finalRecommendations;

    } catch (e) {
      print('❌ Error getting recommended products: $e');
      return [];
    }
  }

  _showProduct(MyProduct product) {
    routeTo(ProductDetailPage.path, data: product);
  }
}
