{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,266,336,415,473,532,586,661,745,828,934,1040,1126,1210,1291,1379,1479,1577,1650,1745,1836,1928,2042,2126,2215,2295,2397,2472,2566,2661,2752,2838,2937,3010,3089,3201,3306,3376,3439,4110,4760,4834,4940,5040,5095,5185,5269,5337,5431,5525,5583,5666,5715,5793,5903,5972,6044,6111,6177,6226,6306,6402,6478,6525,6571,6643,6701,6761,6936,7095,7219,7288,7370,7450,7537,7618,7706,7784,7879,7961,8067,8154,8250,8306,8442,8491,8550,8617,8683,8756,8825,8896,8984,9056,9107", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "120,199,261,331,410,468,527,581,656,740,823,929,1035,1121,1205,1286,1374,1474,1572,1645,1740,1831,1923,2037,2121,2210,2290,2392,2467,2561,2656,2747,2833,2932,3005,3084,3196,3301,3371,3434,4105,4755,4829,4935,5035,5090,5180,5264,5332,5426,5520,5578,5661,5710,5788,5898,5967,6039,6106,6172,6221,6301,6397,6473,6520,6566,6638,6696,6756,6931,7090,7214,7283,7365,7445,7532,7613,7701,7779,7874,7956,8062,8149,8245,8301,8437,8486,8545,8612,8678,8751,8820,8891,8979,9051,9102,9182"}, "to": {"startLines": "199,200,201,202,203,204,205,209,210,211,212,215,217,218,220,225,229,244,247,248,249,251,252,253,255,259,260,261,262,263,264,265,266,267,268,270,273,274,280,281,282,293,294,295,296,297,298,299,300,301,302,303,304,311,312,313,316,317,318,319,323,328,329,330,331,332,337,338,339,340,341,342,346,347,357,358,360,361,365,366,368,373,380,381,452,453,454,456,461,474,475,476,477,478,479,480,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18881,18951,19030,19092,19162,19241,19299,19592,19646,19721,19805,20036,20244,20350,20503,20860,21156,22200,22450,22548,22621,22785,22876,22968,23144,23441,23530,23610,23712,23787,23881,23976,24067,24153,24252,24404,24657,24769,25556,25626,25689,27318,27968,28042,28148,28248,28303,28393,28477,28545,28639,28733,28791,29382,29431,29509,29789,29858,29930,29997,30500,30885,30965,31061,31137,31184,31474,31546,31604,31664,31839,31998,32311,32380,33297,33377,33543,33624,33979,34057,34218,34834,35377,35464,43044,43100,43236,43392,43948,46002,46068,46141,46210,46281,46369,46441,47851", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,75,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "18946,19025,19087,19157,19236,19294,19353,19641,19716,19800,19883,20137,20345,20431,20582,20936,21239,22295,22543,22616,22711,22871,22963,23077,23223,23525,23605,23707,23782,23876,23971,24062,24148,24247,24320,24478,24764,24869,25621,25684,26355,27963,28037,28143,28243,28298,28388,28472,28540,28634,28728,28786,28869,29426,29504,29614,29853,29925,29992,30058,30544,30960,31056,31132,31179,31225,31541,31599,31659,31834,31993,32117,32375,32457,33372,33459,33619,33707,34052,34147,34295,34935,35459,35555,43095,43231,43280,43446,44010,46063,46136,46205,46276,46364,46436,46487,47926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,18387", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,18467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "51,52,98,100,102,123,124,184,185,187,188,191,192,196,514,515,516", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4781,4886,10094,10283,10463,12657,12736,17586,17681,17849,17921,18220,18301,18632,49602,49681,49751", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4881,4968,10183,10377,10548,12731,12824,17676,17761,17916,17987,18296,18382,18700,49676,49746,49864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,289,385,477,560,642,778,874,978,1072,1198,1261,1377,1449,1622,1810,1959,2047,2153,2246,2345,2533,2645,2745,3003,3103,3238,3463,3689,3791,3919,4101,4196,4258,4322,4406,4498,4604,4685,4786,4880,4993,5214,5281,5351,5426,5545,5628,5712,5784,5871,5942,6018,6138,6227,6335,6431,6506,6590,6660,6746,6806,6910,7022,7121,7244,7309,7404,7504,7635,7707,7796,7903,7965,8030,8112,8209,8365,8631,8917,9012,9089,9173,9243,9371,9492,9582,9660,9732,9817,9899,9993,10113,10251,10319,10393,10448,10613,10682,10739,10818,10891,10969,11050,11174,11301,11417,11503,11579,11661,11732", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "125,209,284,380,472,555,637,773,869,973,1067,1193,1256,1372,1444,1617,1805,1954,2042,2148,2241,2340,2528,2640,2740,2998,3098,3233,3458,3684,3786,3914,4096,4191,4253,4317,4401,4493,4599,4680,4781,4875,4988,5209,5276,5346,5421,5540,5623,5707,5779,5866,5937,6013,6133,6222,6330,6426,6501,6585,6655,6741,6801,6905,7017,7116,7239,7304,7399,7499,7630,7702,7791,7898,7960,8025,8107,8204,8360,8626,8912,9007,9084,9168,9238,9366,9487,9577,9655,9727,9812,9894,9988,10108,10246,10314,10388,10443,10608,10677,10734,10813,10886,10964,11045,11169,11296,11412,11498,11574,11656,11727,11890"}, "to": {"startLines": "206,207,208,278,290,291,292,309,322,324,325,356,374,376,379,383,384,385,388,390,392,393,394,395,396,397,398,399,400,401,402,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,451,455,465,466,467,468,469,470,471,472,473,482,483,484,485,486,487,488,489,490,491,492,493,494,495,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19358,19433,19517,25263,27061,27153,27236,29182,30404,30549,30653,33171,34940,35066,35305,35621,35794,35982,36284,36478,36681,36774,36873,37061,37173,37273,37531,37631,37766,37991,38217,38406,38534,38716,38811,38873,38937,39021,39113,39219,39300,39401,39495,39608,39829,39896,39966,40041,40160,40243,40327,40399,40486,40557,40633,41188,41277,41385,41481,41556,41640,41710,41796,41856,41960,42072,42171,42294,42359,42454,42554,42685,42955,43285,44816,44878,44943,45025,45122,45278,45544,45830,45925,46547,46631,46701,46829,46950,47040,47118,47190,47275,47357,47451,47571,47709,47777,47931,47986,48151,48220,48277,48356,48429,48507,48588,48712,48839,48955,49041,49117,49199,49270", "endColumns": "74,83,74,95,91,82,81,135,95,103,93,125,62,115,71,172,187,148,87,105,92,98,187,111,99,257,99,134,224,225,101,127,181,94,61,63,83,91,105,80,100,93,112,220,66,69,74,118,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,130,71,88,106,61,64,81,96,155,265,285,94,76,83,69,127,120,89,77,71,84,81,93,119,137,67,73,54,164,68,56,78,72,77,80,123,126,115,85,75,81,70,162", "endOffsets": "19428,19512,19587,25354,27148,27231,27313,29313,30495,30648,30742,33292,34998,35177,35372,35789,35977,36126,36367,36579,36769,36868,37056,37168,37268,37526,37626,37761,37986,38212,38314,38529,38711,38806,38868,38932,39016,39108,39214,39295,39396,39490,39603,39824,39891,39961,40036,40155,40238,40322,40394,40481,40552,40628,40748,41272,41380,41476,41551,41635,41705,41791,41851,41955,42067,42166,42289,42354,42449,42549,42680,42752,43039,43387,44873,44938,45020,45117,45273,45539,45825,45920,45997,46626,46696,46824,46945,47035,47113,47185,47270,47352,47446,47566,47704,47772,47846,47981,48146,48215,48272,48351,48424,48502,48583,48707,48834,48950,49036,49112,49194,49265,49428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,211", "endColumns": "74,80,76", "endOffsets": "125,206,283"}, "to": {"startLines": "53,101,106", "startColumns": "4,4,4", "startOffsets": "4973,10382,10775", "endColumns": "74,80,76", "endOffsets": "5043,10458,10847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "97,108,109,110", "startColumns": "4,4,4,4", "startOffsets": "9989,10948,11048,11162", "endColumns": "104,99,113,101", "endOffsets": "10089,11043,11157,11259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,340,444,641,688,755,853,927,1149,1234,1342,1411,1465,1529,1606,1699,1989,2062,2127,2181,2234,2311,2429,2540,2597,2679,2758,2843,2909,3015,3283,3366,3443,3506,3567,3629,3690,3761,3843,3949,4046,4133,4236,4328,4404,4483,4568,4766,4964,5072,5201,5263,5903,6002,6064", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "172,335,439,636,683,750,848,922,1144,1229,1337,1406,1460,1524,1601,1694,1984,2057,2122,2176,2229,2306,2424,2535,2592,2674,2753,2838,2904,3010,3278,3361,3438,3501,3562,3624,3685,3756,3838,3944,4041,4128,4231,4323,4399,4478,4563,4761,4959,5067,5196,5258,5898,5997,6059,6114"}, "to": {"startLines": "275,276,277,279,283,284,285,286,287,288,289,305,308,310,314,315,320,326,327,335,345,349,350,351,352,353,359,362,367,369,370,371,372,375,377,378,382,386,387,389,391,403,428,429,430,431,432,450,457,458,459,460,462,463,464,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24874,24996,25159,25359,26360,26407,26474,26572,26646,26868,26953,28874,29128,29318,29619,29696,30063,30747,30820,31356,32258,32541,32618,32736,32847,32904,33464,33712,34152,34300,34406,34674,34757,35003,35182,35243,35560,36131,36202,36372,36584,38319,40753,40856,40948,41024,41103,42757,43451,43649,43757,43886,44015,44655,44754,46492", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,76,92,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,98,61,54", "endOffsets": "24991,25154,25258,25551,26402,26469,26567,26641,26863,26948,27056,28938,29177,29377,29691,29784,30348,30815,30880,31405,32306,32613,32731,32842,32899,32981,33538,33792,34213,34401,34669,34752,34829,35061,35238,35300,35616,36197,36279,36473,36676,38401,40851,40943,41019,41098,41183,42950,43644,43752,43881,43943,44650,44749,44811,46542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3081,3192", "endColumns": "110,121", "endOffsets": "3187,3309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "85", "startColumns": "4", "startOffsets": "8632", "endColumns": "131", "endOffsets": "8759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,198,262,347,413,481,539,617,693,762,824", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "134,193,257,342,408,476,534,612,688,757,819,886"}, "to": {"startLines": "214,224,226,227,228,232,240,243,246,250,254,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19952,20801,20941,21005,21090,21393,21933,22122,22374,22716,23082,23374", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "20031,20855,21000,21085,21151,21456,21986,22195,22445,22780,23139,23436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,381,513,641,770,901,1035,1135,1269,1402", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "161,256,376,508,636,765,896,1030,1130,1264,1397,1520"}, "to": {"startLines": "95,99,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9807,10188,11264,11384,11516,11644,11773,11904,12038,12138,12272,12405", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "9913,10278,11379,11511,11639,11768,11899,12033,12133,12267,12400,12523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "121,521", "startColumns": "4,4", "startOffsets": "12528,50207", "endColumns": "60,77", "endOffsets": "12584,50280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,221,288,374,441,502,587,651,717,785,864,924,985,1059,1123,1191,1254,1328,1394,1474,1553,1634,1727,1827,1912,1963,2011,2089,2153,2218,2289,2389,2474,2565", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "114,216,283,369,436,497,582,646,712,780,859,919,980,1054,1118,1186,1249,1323,1389,1469,1548,1629,1722,1822,1907,1958,2006,2084,2148,2213,2284,2384,2469,2560,2651"}, "to": {"startLines": "213,216,219,221,222,223,230,231,233,234,235,236,237,238,239,241,242,245,256,257,269,271,272,306,307,321,333,334,336,343,344,354,355,363,364", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19888,20142,20436,20587,20673,20740,21244,21329,21461,21527,21595,21674,21734,21795,21869,21991,22059,22300,23228,23294,24325,24483,24564,28943,29043,30353,31230,31278,31410,32122,32187,32986,33086,33797,33888", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "19947,20239,20498,20668,20735,20796,21324,21388,21522,21590,21669,21729,21790,21864,21928,22054,22117,22369,23289,23369,24399,24559,24652,29038,29123,30399,31273,31351,31469,32182,32253,33081,33166,33883,33974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "517,518", "startColumns": "4,4", "startOffsets": "49869,49952", "endColumns": "82,84", "endOffsets": "49947,50032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "41,42,43,44,45,46,47,198", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3745,3843,3950,4047,4146,4250,4354,18780", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3838,3945,4042,4141,4245,4349,4466,18876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,356,497,612,716,808,886,977,1067,1179,1293,1383,1473,1580,1705,1788,1873,2058,2153,2257,2371,2476", "endColumns": "164,135,140,114,103,91,77,90,89,111,113,89,89,106,124,82,84,184,94,103,113,104,150", "endOffsets": "215,351,492,607,711,803,881,972,1062,1174,1288,1378,1468,1575,1700,1783,1868,2053,2148,2252,2366,2471,2622"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5048,5213,5349,5490,5605,5709,5801,5879,5970,6060,6172,6286,6376,6466,6573,6698,6781,6866,7051,7146,7250,7364,7469", "endColumns": "164,135,140,114,103,91,77,90,89,111,113,89,89,106,124,82,84,184,94,103,113,104,150", "endOffsets": "5208,5344,5485,5600,5704,5796,5874,5965,6055,6167,6281,6371,6461,6568,6693,6776,6861,7046,7141,7245,7359,7464,7615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "78", "endOffsets": "129"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "32462", "endColumns": "78", "endOffsets": "32536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "77,78,79,80,81,82,83,84,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7620,7726,7883,8013,8123,8280,8410,8525,8764,8914,9021,9178,9306,9453,9596,9664,9726", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "7721,7878,8008,8118,8275,8405,8520,8627,8909,9016,9173,9301,9448,9591,9659,9721,9802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,103,104,107,122,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,189,194,195,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3314,3392,3470,3555,3652,4471,4567,4697,10553,10620,10852,12589,12829,12892,13000,13060,13126,13182,13253,13313,13367,13493,13550,13612,13666,13741,13875,13960,14038,14133,14218,14299,14436,14520,14606,14739,14830,14908,14964,15019,15085,15159,15237,15308,15390,15462,15539,15619,15693,15800,15893,15966,16058,16154,16228,16304,16400,16452,16534,16601,16688,16775,16837,16901,16964,17034,17140,17256,17353,17467,17527,17992,18472,18555,18705", "endLines": "6,36,37,38,39,40,48,49,50,103,104,107,122,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,189,194,195,197", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3387,3465,3550,3647,3740,4562,4692,4776,10615,10683,10943,12652,12887,12995,13055,13121,13177,13248,13308,13362,13488,13545,13607,13661,13736,13870,13955,14033,14128,14213,14294,14431,14515,14601,14734,14825,14903,14959,15014,15080,15154,15232,15303,15385,15457,15534,15614,15688,15795,15888,15961,16053,16149,16223,16299,16395,16447,16529,16596,16683,16770,16832,16896,16959,17029,17135,17251,17348,17462,17522,17581,18067,18550,18627,18775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "96,105,186,190,513,519,520", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9918,10688,17766,18072,49433,50037,50124", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "9984,10770,17844,18215,49597,50119,50202"}}]}]}