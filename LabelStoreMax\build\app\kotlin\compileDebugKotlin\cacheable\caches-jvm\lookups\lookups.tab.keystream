  Application android.app  GeneratedPluginRegistrant android.app.Activity  println android.app.Activity  onCreate android.app.Application  println android.app.Application  GeneratedPluginRegistrant android.content.Context  println android.content.Context  GeneratedPluginRegistrant android.content.ContextWrapper  println android.content.ContextWrapper  GeneratedPluginRegistrant  android.view.ContextThemeWrapper  println  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  
FlutterEngine #androidx.activity.ComponentActivity  GeneratedPluginRegistrant #androidx.activity.ComponentActivity  println #androidx.activity.ComponentActivity  GeneratedPluginRegistrant -androidx.activity.ComponentActivity.Companion  println -androidx.activity.ComponentActivity.Companion  
FlutterEngine #androidx.core.app.ComponentActivity  GeneratedPluginRegistrant #androidx.core.app.ComponentActivity  println #androidx.core.app.ComponentActivity  GeneratedPluginRegistrant &androidx.fragment.app.FragmentActivity  println &androidx.fragment.app.FragmentActivity  Application com.velvete.ly  
FlutterEngine com.velvete.ly  FlutterFragmentActivity com.velvete.ly  GeneratedPluginRegistrant com.velvete.ly  MainActivity com.velvete.ly  MainApplication com.velvete.ly  println com.velvete.ly  GeneratedPluginRegistrant com.velvete.ly.MainActivity  println com.velvete.ly.MainActivity  println com.velvete.ly.MainApplication  FlutterFragmentActivity io.flutter.embedding.android  GeneratedPluginRegistrant 4io.flutter.embedding.android.FlutterFragmentActivity  configureFlutterEngine 4io.flutter.embedding.android.FlutterFragmentActivity  println 4io.flutter.embedding.android.FlutterFragmentActivity  
FlutterEngine io.flutter.embedding.engine  GeneratedPluginRegistrant io.flutter.plugins  registerWith ,io.flutter.plugins.GeneratedPluginRegistrant  println 	kotlin.io                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       