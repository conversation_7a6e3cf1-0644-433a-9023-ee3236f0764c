// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/foundation.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart'; // Ensure Nylo is imported for getEnv and Dio
import 'dart:async'; // For request deduplication
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';

// Cache entry for request deduplication
class _CacheEntry {
  final dynamic data;
  final DateTime timestamp;

  _CacheEntry(this.data, this.timestamp);

  bool get isExpired => DateTime.now().difference(timestamp) > WooCommerceService._cacheTTL;
}

class WooCommerceService {
  static final WooCommerceService _instance = WooCommerceService._internal();
  late WooCommerce _wooCommerce;

  // Request deduplication: Cache for ongoing API requests
  final Map<String, Future<dynamic>> _ongoingRequests = {};

  // Simple cache for frequently accessed data (5 minute TTL)
  final Map<String, _CacheEntry> _cache = {};
  static const Duration _cacheTTL = Duration(minutes: 5);

  factory WooCommerceService() {
    return _instance;
  }

  // Request deduplication helper methods
  String _generateCacheKey(String endpoint, Map<String, dynamic> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
    );
    return '$endpoint:${sortedParams.toString()}';
  }

  Future<T> _deduplicatedRequest<T>(
    String cacheKey,
    Future<T> Function() apiCall,
    {bool useCache = true}
  ) async {
    // Check cache first if enabled
    if (useCache && _cache.containsKey(cacheKey)) {
      final entry = _cache[cacheKey]!;
      if (!entry.isExpired) {
        if (kDebugMode) {
          print('📦 Cache hit for: $cacheKey');
        }
        return entry.data as T;
      } else {
        _cache.remove(cacheKey);
      }
    }

    // Check if request is already in progress
    if (_ongoingRequests.containsKey(cacheKey)) {
      if (kDebugMode) {
        print('🔄 Request deduplication for: $cacheKey');
      }
      return await _ongoingRequests[cacheKey] as T;
    }

    // Start new request
    final future = apiCall();
    _ongoingRequests[cacheKey] = future;

    try {
      final result = await future;

      // Cache the result if caching is enabled
      if (useCache) {
        _cache[cacheKey] = _CacheEntry(result, DateTime.now());
      }

      return result;
    } finally {
      // Always remove from ongoing requests when done
      _ongoingRequests.remove(cacheKey);
    }
  }

  WooCommerceService._internal() {
    // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the lines below to reactivate WooCommerce credentials debugging
    // print('🔍 ===== WOOCOMMERCE CREDENTIALS DEBUG =====');

    String siteUrl = getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly');
    String consumerKey = getEnv('WOO_CONSUMER_KEY');
    String consumerSecret = getEnv('WOO_CONSUMER_SECRET');

    // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the lines below to reactivate credential logging
    // print('📍 Raw getEnv results:');
    // print('   WOO_CONSUMER_KEY from getEnv(): "$consumerKey"');
    // print('   WOO_CONSUMER_SECRET from getEnv(): "$consumerSecret"');
    // print('   WOO_BASE_URL from getEnv(): "$siteUrl"');

    String credentialSource = '';

    // Log if we're using fallback credentials
    if (consumerKey.isEmpty) {
      consumerKey = 'ck_65b8ab7eae9c91b4b305ef99598a80b050f47eac';
      credentialSource += 'CONSUMER_KEY: fallback (hardcoded), ';
      print('⚠️ WARNING: Using fallback WOO_CONSUMER_KEY - check .env file');
    } else {
      credentialSource += 'CONSUMER_KEY: .env file, ';
      print('✅ Using WOO_CONSUMER_KEY from .env file');
    }

    if (consumerSecret.isEmpty) {
      consumerSecret = 'cs_5b3ea8958251dd4febf533813ac9bb7b631a94ef';
      credentialSource += 'CONSUMER_SECRET: fallback (hardcoded)';
      print('⚠️ WARNING: Using fallback WOO_CONSUMER_SECRET - check .env file');
    } else {
      credentialSource += 'CONSUMER_SECRET: .env file';
      print('✅ Using WOO_CONSUMER_SECRET from .env file');
    }

    print('📋 CREDENTIAL SOURCE SUMMARY: $credentialSource');
    print('📋 FINAL CREDENTIALS TO BE USED:');
    print('   Consumer Key: $consumerKey');
    print('   Consumer Secret: $consumerSecret');
    print('==========================================');

    // Use site URL only - woocommerce_flutter_api automatically appends /wp-json/wc/v3
    String baseUrl = siteUrl;

    print('=== WooCommerce Service Initialization ===');
    print('Site URL: $siteUrl');
    print('Base URL for WooCommerce API: $baseUrl');
    print('Expected API endpoints will be: $baseUrl/wp-json/wc/v3/...');
    print('Consumer Key: ${consumerKey.substring(0, 10)}...');
    print('Consumer Secret: ${consumerSecret.substring(0, 10)}...');
    print('==========================================');

    _wooCommerce = WooCommerce(
      baseUrl: baseUrl,
      username: consumerKey,
      password: consumerSecret,
      useFaker: false,
      isDebug: getEnv('APP_DEBUG', defaultValue: 'true') == 'true',
    );
  }

  WooCommerce get wooCommerce => _wooCommerce;



  // Helper method to parse products in isolate to avoid blocking UI
  static List<MyProduct> _parseProductsInIsolate(List<dynamic> jsonList) {
    return jsonList.map((json) => MyProduct.fromJson(json)).toList();
  }

  // Convenience methods for common operations
  Future<List<MyProduct>> getProducts({
    int page = 1,
    int perPage = 10,
    String? search,
    int? category,
    List<int>? include,
    WooSortOrderBy orderBy = WooSortOrderBy.date,
    WooSortOrder order = WooSortOrder.desc,
    WooFilterStatus status = WooFilterStatus.publish,
    WooProductStockStatus? stockStatus,
  }) async {
    // Generate cache key for request deduplication
    final params = {
      'page': page,
      'perPage': perPage,
      if (search != null) 'search': search,
      if (category != null) 'category': category,
      if (include != null) 'include': include.join(','),
      'orderBy': orderBy.name,
      'order': order.name,
      'status': status.name,
      if (stockStatus != null) 'stockStatus': stockStatus.name,
    };
    final cacheKey = _generateCacheKey('products', params);

    // Use request deduplication
    return await _deduplicatedRequest<List<MyProduct>>(
      cacheKey,
      () async {
      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the block below to reactivate products API call debugging
      // if (kDebugMode) {
      //   print('=== WooCommerce getProducts API Call ===');
      //   print('Base URL from env: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}');
      //   print('Expected full URL: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}/wp-json/wc/v3/products');
      //   print('Parameters: page=$page, perPage=$perPage, search=$search, category=$category');
      //
      //   // Log the actual credentials being used by the WooCommerce instance
      //   print('🔍 ACTUAL CREDENTIALS IN USE:');
      //   print('   WooCommerce baseUrl: ${_wooCommerce.baseUrl}');
      //   print('   WooCommerce username (consumer key): ${_wooCommerce.username}');
      //   print('   WooCommerce password (consumer secret): ${_wooCommerce.password}');
      //   print('========================================');
      // }

    try {
      // Use direct HTTP call to bypass WooProduct.fromJson issues
      final response = await _wooCommerce.dio.get('/products', queryParameters: {
        'page': page,
        'per_page': perPage,
        if (search != null) 'search': search,
        if (category != null) 'category': category,
        if (include != null) 'include': include.join(','),
        'orderby': orderBy.name,
        'order': order.name,
        'status': status.name,
        if (stockStatus != null) 'stock_status': stockStatus.name,
      });

      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the block below to reactivate products response debugging
      // if (kDebugMode) {
      //   print('✅ Products API call successful, returned ${response.data.length} products');
      //
      //   // DIAGNOSTIC LOGGING: Log raw price data from API for each product
      //   for (int i = 0; i < (response.data as List).length && i < 5; i++) { // Log first 5 products to avoid spam
      //     var productJson = (response.data as List)[i];
      //     print('🔍 API Raw Price Data for Product ID ${productJson['id']}: price=${productJson['price']}, regular_price=${productJson['regular_price']}, sale_price=${productJson['sale_price']}, on_sale=${productJson['on_sale']}');
      //   }
      // }

      // Parse JSON in isolate to avoid blocking UI thread for large product lists
      final myProducts = await compute(_parseProductsInIsolate, response.data as List);
      if (kDebugMode) {
        print('✅ Converted to ${myProducts.length} MyProduct objects');
      }
      return myProducts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Products API call failed with error: $e');
      }

      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the block below to reactivate detailed DIO exception logging
      // Detailed DioException logging (only in debug mode for performance)
      // if (kDebugMode && e is DioException) {
      //   print('=== DETAILED DIO EXCEPTION ANALYSIS ===');
      //   print('DioException Type: ${e.type}');
      //   print('Status Code: ${e.response?.statusCode}');
      //   print('Response Data: ${e.response?.data}');
      //   print('Response Headers: ${e.response?.headers}');
      //   print('Error Message: ${e.message}');
      //   print('Underlying Error: ${e.error}');
      //   print('Request URL: ${e.requestOptions.uri}');
      //   print('Request Method: ${e.requestOptions.method}');
      //   print('Request Headers: ${e.requestOptions.headers}');
      //   print('Request Data: ${e.requestOptions.data}');
      //   print('======================================');
      // }

      rethrow;
    }
    });
  }

  Future<MyProduct> getProduct(int id) async {
    try {
      // Use direct HTTP call to bypass WooProduct.fromJson issues
      final response = await _wooCommerce.dio.get('/products/$id');

      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the block below to reactivate single product debugging
      // DIAGNOSTIC LOGGING: Log raw price data from API for single product
      // if (kDebugMode) {
      //   var productJson = response.data;
      //   print('🔍 API Raw Price Data for Single Product ID ${productJson['id']}: price=${productJson['price']}, regular_price=${productJson['regular_price']}, sale_price=${productJson['sale_price']}, on_sale=${productJson['on_sale']}');
      // }

      // Parse JSON directly into MyProduct to avoid WooProduct null safety issues
      return MyProduct.fromJson(response.data);
    } catch (e) {
      print('❌ Single product API call failed with error: $e');
      rethrow;
    }
  }

  Future<List<MyProductCategory>> getProductCategories({
    int page = 1,
    int perPage = 10,
    int? parent,
    bool? hideEmpty,
    List<int>? include,
    WooSortOrder order = WooSortOrder.asc,
    WooCategoryOrderBy orderBy = WooCategoryOrderBy.name,
  }) async {
    // Generate cache key for request deduplication
    final params = {
      'page': page,
      'perPage': perPage,
      if (parent != null) 'parent': parent,
      if (hideEmpty != null) 'hideEmpty': hideEmpty,
      if (include != null) 'include': include.join(','),
      'order': order.name,
      'orderBy': orderBy.name,
    };
    final cacheKey = _generateCacheKey('categories', params);

    // Use request deduplication
    return await _deduplicatedRequest<List<MyProductCategory>>(
      cacheKey,
      () async {
        if (kDebugMode) {
          print('=== WooCommerce getCategories API Call ===');
          print('Base URL from env: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}');
          print('Expected full URL: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}/wp-json/wc/v3/products/categories');
          print('Parameters: page=$page, perPage=$perPage, parent=$parent, hideEmpty=$hideEmpty');
          print('==========================================');
        }

    try {
      // Use direct HTTP call to bypass WooProductCategory.fromJson issues
      final response = await _wooCommerce.dio.get('/products/categories', queryParameters: {
        'page': page,
        'per_page': perPage,
        if (parent != null) 'parent': parent,
        if (hideEmpty != null) 'hide_empty': hideEmpty,
        if (include != null) 'include': include.join(','),
        'order': order.name,
        'orderby': orderBy.name,
      });

        if (kDebugMode) {
          print('✅ Categories API call successful, returned ${response.data.length} categories');
        }

        // Parse JSON directly into MyProductCategory to avoid WooProductCategory null safety issues
        final myCategories = (response.data as List).map((json) => MyProductCategory.fromJson(json)).toList();
        if (kDebugMode) {
          print('✅ Converted to ${myCategories.length} MyProductCategory objects');
        }
        return myCategories;
      } catch (e) {
        if (kDebugMode) {
          print('❌ Categories API call failed with error: $e');

          // Detailed DioException logging (only in debug mode for performance)
          if (e is DioException) {
            print('=== DETAILED DIO EXCEPTION ANALYSIS (Categories) ===');
            print('DioException Type: ${e.type}');
            print('Status Code: ${e.response?.statusCode}');
            print('Response Data: ${e.response?.data}');
            print('Response Headers: ${e.response?.headers}');
            print('Error Message: ${e.message}');
            print('Underlying Error: ${e.error}');
            print('Request URL: ${e.requestOptions.uri}');
            print('Request Method: ${e.requestOptions.method}');
            print('Request Headers: ${e.requestOptions.headers}');
            print('Request Data: ${e.requestOptions.data}');
            print('===================================================');
          }
        }

        rethrow;
      }
    });
  }

  Future<List<MyProductVariation>> getProductVariations(int productId, {
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      print('=== WooCommerce getProductVariations API Call ===');
      print('Product ID: $productId');
      print('Parameters: page=$page, perPage=$perPage');
      print('================================================');

      // Use direct HTTP call to bypass WooProductVariation.fromJson issues
      final response = await _wooCommerce.dio.get('/products/$productId/variations', queryParameters: {
        'page': page,
        'per_page': perPage,
      });

      print('✅ Product variations API call successful, returned ${response.data.length} variations');

      // Parse JSON directly into MyProductVariation to avoid WooProductVariation null safety issues
      final myVariations = (response.data as List).map((json) => MyProductVariation.fromJson(json)).toList();
      print('✅ Converted to ${myVariations.length} MyProductVariation objects');
      return myVariations;
    } catch (e) {
      print('❌ Product variations API call failed with error: $e');
      rethrow;
    }
  }

  Future<WooOrder> createOrder(WooOrder order) async {
    print('🌐 ===== WOOCOMMERCE CREATE ORDER API CALL =====');
    print('📍 Base URL: ${_wooCommerce.baseUrl}');
    print('📍 Full URL: ${_wooCommerce.baseUrl}/orders');
    print('🔧 METHOD: POST');
    print('📦 ORDER PAYLOAD SUMMARY:');
    print('   Payment Method: ${order.paymentMethod}');
    print('   Status: ${order.status}');
    print('   Line Items: ${order.lineItems?.length ?? 0}');
    print('============================================');

    try {
      WooOrder createdOrder = await _wooCommerce.createOrder(order);

      print('🌐 ===== WOOCOMMERCE CREATE ORDER RESPONSE =====');
      print('✅ SUCCESS - Order created via WooCommerce API');
      print('📊 Order ID: ${createdOrder.id}');
      print('📧 Order Number: ${createdOrder.number}');
      print('💰 Order Total: ${createdOrder.total}');
      print('📋 Order Status: ${createdOrder.status}');
      print('🔗 Order Key: ${createdOrder.orderKey}');
      print('=============================================');

      return createdOrder;
    } catch (e, stackTrace) {
      print('🌐 ===== WOOCOMMERCE CREATE ORDER ERROR =====');
      print('❌ FAILED - WooCommerce API Error: $e');
      print('📋 Stack Trace: $stackTrace');
      print('==========================================');
      rethrow;
    }
  }

  Future<List<WooOrder>> getOrders({
    int page = 1,
    int perPage = 10,
    int? customer,
    List<WooOrderStatus> status = const [WooOrderStatus.any],
  }) async {
    return await _wooCommerce.getOrders(
      page: page,
      perPage: perPage,
      customer: customer,
      status: status,
    );
  }

  Future<WooOrder> getOrder(int id) async {
    return await _wooCommerce.getOrder(id);
  }

  // Add this method to your WooCommerceService class
  Future<List<WooOrder>> getOrdersByCustomerId(int customerId) async {
    try {
      final response = await _wooCommerce.dio.get(
        '/orders',
        queryParameters: {
          'customer': customerId.toString(),
          'per_page': '100', // Get up to 100 orders
        },
      );

      if (response.data is List) {
        return (response.data as List).map((order) => WooOrder.fromJson(order)).toList();
      }
      return [];
    } catch (e) {
      print('❌ Error fetching orders for customer $customerId: $e');
      return [];
    }
  }

  // WooCustomer methods
  Future<WooCustomer> createCustomer(WooCustomer customer) async {
    print('🌐 ===== WOOCOMMERCE CREATE CUSTOMER API CALL =====');
    print('📍 Base URL: ${_wooCommerce.baseUrl}');
    print('📍 Full URL: ${_wooCommerce.baseUrl}/wp-json/wc/v3/customers');
    print('🔧 METHOD: POST');
    print('📦 CUSTOMER PAYLOAD:');
    print('   Email: ${customer.email}');
    print('   First Name: ${customer.firstName}');
    print('   Last Name: ${customer.lastName}');
    print('   Username: ${customer.username}');
    print('   Password: ${customer.password != null ? "***MASKED***" : "null"}');
    print('===============================================');

    try {
      // Use direct HTTP call to bypass WooCustomer.fromJson null safety issues
      final response = await _wooCommerce.dio.post('/customers', data: {
        'email': customer.email,
        'first_name': customer.firstName,
        'last_name': customer.lastName,
        'username': customer.username,
        'password': customer.password,
        if (customer.billing != null) 'billing': customer.billing!.toJson(),
        if (customer.shipping != null) 'shipping': customer.shipping!.toJson(),
        if (customer.metaData?.isNotEmpty == true) 'meta_data': customer.metaData!.map((e) => e.toJson()).toList(),
      });

      print('🌐 ===== WOOCOMMERCE CREATE CUSTOMER RESPONSE =====');
      print('✅ SUCCESS - Customer created via WooCommerce API');
      print('🌐 RESPONSE BODY: ${response.data}');

      // Parse response using null-safe wrapper
      MyWooCustomer myCustomer = MyWooCustomer.fromJson(response.data);
      print('📊 Customer ID: ${myCustomer.id}');
      print('📧 Email: ${myCustomer.email}');
      print('👤 Name: ${myCustomer.firstName} ${myCustomer.lastName}');
      print('🔑 Username: ${myCustomer.username}');
      print('================================================');

      // Convert back to WooCustomer for compatibility
      return myCustomer.toWooCustomer();
    } on DioException catch (e) {
      print('🌐 ===== WOOCOMMERCE CREATE CUSTOMER ERROR =====');
      print('❌ FAILED - WooCommerce Customer API Error: DioException [bad response]: ${e.message}');

      // CRITICAL: Log the server's response data to identify the specific validation error
      print('🌐 WooCommerce Server Response Data: ${e.response?.data}');
      print('🌐 HTTP Status Code: ${e.response?.statusCode}');
      print('🌐 Response Headers: ${e.response?.headers}');
      print('🌐 Request URL: ${e.requestOptions.uri}');
      print('🌐 Request Method: ${e.requestOptions.method}');
      print('🌐 Request Headers: ${e.requestOptions.headers}');
      print('🌐 Request Data: ${e.requestOptions.data}');

      if (e.response?.statusCode == 400) {
        print('The status code of 400 has the following meaning: "Client error - the request contains bad syntax or cannot be fulfilled"');
      }

      print('📋 Stack Trace: ${e.stackTrace}');
      print('=============================================');
      rethrow;
    } catch (e, stackTrace) {
      print('🌐 ===== WOOCOMMERCE CREATE CUSTOMER ERROR (NON-DIO) =====');
      print('❌ FAILED - Non-Dio Error: $e');
      print('📋 Stack Trace: $stackTrace');
      print('========================================================');
      rethrow;
    }
  }

  Future<WooCustomer> getCustomer(int id) async {
    return await _wooCommerce.getCustomer(id);
  }

  Future<WooCustomer> updateCustomer(WooCustomer customer) async {
    return await _wooCommerce.updateCustomer(customer);
  }

  // Enhanced authentication methods for Phase 2.2
  Future<List<WooCustomer>> getCustomersByEmail(String email) async {
    try {
      // Add logging for the URL being called
      print('🔍 Attempting to fetch customer by email: ${getEnv('WOO_BASE_URL')}/wp-json/wc/v3/customers?search=$email');

      // Log the actual credentials being used for this API call
      print('🔍 CUSTOMER API CREDENTIALS IN USE:');
      print('   WooCommerce baseUrl: ${_wooCommerce.baseUrl}');
      print('   WooCommerce username (consumer key): ${_wooCommerce.username}');
      print('   WooCommerce password (consumer secret): ${_wooCommerce.password}');

      return await _wooCommerce.getCustomers(email: email);
    } on DioException catch (e) {
      print('❌ Error getting customers by email: DioException [${e.response?.statusCode ?? 'N/A'}] - ${e.message}');
      if (e.response?.statusCode == 404 || e.response?.statusCode == 403) {
        throw e; // Re-throw for explicit handling by AuthService
      }
      return []; // For other types of Dio errors, return empty list (or handle as appropriate)
    } catch (e) {
      print('❌ Error getting customers by email: $e');
      return []; // For non-Dio exceptions
    }
  }

  Future<WooCustomer?> authenticateCustomer(String email, String password) async {
    try {
      // First, find customer by email
      List<WooCustomer> customers = await getCustomersByEmail(email);

      if (customers.isEmpty) {
        print('No customer found with email: $email');
        return null;
      }

      WooCustomer customer = customers.first;

      // Note: WooCommerce REST API doesn't directly support password verification
      // In a production environment, you would typically use:
      // 1. WordPress JWT authentication
      // 2. Custom authentication endpoint
      // 3. OAuth integration

      // For now, we'll validate that the customer exists and has the correct email
      // This is a simplified authentication for development purposes
      if (customer.email?.toLowerCase() == email.toLowerCase()) {
        return customer;
      }

      return null;
    } catch (e) {
      print('Error authenticating customer: $e');
      return null;
    }
  }

  Future<bool> validateCustomerCredentials(String email, String password) async {
    WooCustomer? customer = await authenticateCustomer(email, password);
    return customer != null;
  }

  // WooCoupon methods
  Future<List<WooCoupon>> getCoupons({
    int page = 1,
    int perPage = 10,
  }) async {
    return await _wooCommerce.getCoupons(
      page: page,
      perPage: perPage,
    );
  }

  // Shipping methods
  Future<List<WooShippingZone>> getShippingZones() async {
    return await _wooCommerce.getShippingZones();
  }

  Future<WooShippingMethod> getShippingMethod(int id) async {
    return await _wooCommerce.getShippingMethod(id: id.toString());
  }



  // Product reviews
  Future<List<WooProductReview>> getProductReviews({
    int page = 1,
    int perPage = 10,
    List<int>? product,
  }) async {
    print('=== WooCommerce getProductReviews API Call ===');
    print('Base URL from env: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}');
    print('Expected full URL: ${getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly')}/wp-json/wc/v3/products/reviews');
    print('Parameters: page=$page, perPage=$perPage, product=$product');
    print('==============================================');

    try {
      // Build query parameters carefully
      Map<String, dynamic> queryParams = {
        'page': page,
        'per_page': perPage,
      };

      // Add product filter if specified
      if (product != null && product.isNotEmpty) {
        // WooCommerce API expects 'product' parameter as comma-separated string
        queryParams['product'] = product.join(',');
        // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the line below to reactivate reviews filtering debugging
        // print('📋 Filtering reviews for product IDs: ${product.join(',')}');
      } else {
        // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the line below to reactivate reviews debugging
        // print('📋 Fetching all reviews (no product filter)');
      }

      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the line below to reactivate query parameters debugging
      // print('📋 Query parameters: $queryParams');

      // Use direct HTTP call to get better error handling and debugging
      final response = await _wooCommerce.dio.get('/products/reviews', queryParameters: queryParams);

      // TODO: PRODUCTS_FETCHING_DEBUG - Uncomment the line below to reactivate reviews response debugging
      // print('✅ Product reviews API call successful, returned ${response.data.length} reviews');

      // Parse JSON directly into WooProductReview objects
      final reviews = (response.data as List).map((json) => WooProductReview.fromJson(json)).toList();
      print('✅ Converted to ${reviews.length} WooProductReview objects');
      return reviews;
    } catch (e) {
      print('❌ Product reviews API call failed with error: $e');
      if (e.toString().contains('DioException')) {
        print('❌ Detailed error information:');
        print('   Error type: ${e.runtimeType}');
        print('   Error message: ${e.toString()}');
        // Try to get more detailed error info
        try {
          final dioError = e as DioException;
          print('   Status code: ${dioError.response?.statusCode}');
          print('   Response data: ${dioError.response?.data}');
          print('   Request URL: ${dioError.requestOptions.uri}');
          print('   Request method: ${dioError.requestOptions.method}');
          print('   Request headers: ${dioError.requestOptions.headers}');
        } catch (parseError) {
          print('   Could not parse DioException details: $parseError');
        }
      }
      rethrow;
    }
  }

  Future<WooProductReview> createProductReview(int productId, WooProductReview review) async {
    return await _wooCommerce.createProductReview(review);
  }

  /// Fetch dynamic shipping cost based on selected city
  /// Calls the custom REST API endpoint: /wp-json/v-shipping/v1/shipping-cost-by-city
  Future<double> fetchDynamicShippingCost(String city) async {
    try {
      print('🚚 ===== DYNAMIC SHIPPING COST API CALL =====');

      String baseUrl = getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly');
      String apiUrl = '$baseUrl/wp-json/v-shipping/v1/shipping-cost-by-city';

      print('🔍 City: $city');
      print('🔍 API URL: $apiUrl');
      print('🔍 Full request URL: $apiUrl?city=$city');

      // Create Dio instance for the API call
      Dio dio = Dio();

      // Make GET request with city parameter
      Response response = await dio.get(
        apiUrl,
        queryParameters: {'city': city},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          validateStatus: (status) => status! < 500, // Accept 4xx errors for handling
        ),
      );

      print('🔍 Response status: ${response.statusCode}');
      print('🔍 Response data: ${response.data}');

      if (response.statusCode == 200) {
        // Parse successful response
        Map<String, dynamic> data = response.data;

        if (data.containsKey('cost')) {
          double cost = double.tryParse(data['cost'].toString()) ?? 0.0;
          print('✅ Dynamic shipping cost fetched successfully: $cost LYD');
          return cost;
        } else {
          print('❌ Response missing cost field');
          print('❌ Available fields: ${data.keys.toList()}');
          return 0.0;
        }
      } else if (response.statusCode == 404) {
        print('❌ City not found in backend: $city');
        print('❌ Response: ${response.data}');
        return 0.0;
      } else {
        print('❌ API returned error status: ${response.statusCode}');
        print('❌ Response: ${response.data}');
        return 0.0;
      }

    } catch (e) {
      print('❌ Dynamic shipping cost API call failed with error: $e');

      if (e is DioException) {
        print('❌ Detailed DioException information:');
        print('   Status code: ${e.response?.statusCode}');
        print('   Response data: ${e.response?.data}');
        print('   Request URL: ${e.requestOptions.uri}');
        print('   Request method: ${e.requestOptions.method}');
        print('   Error type: ${e.type}');
        print('   Error message: ${e.message}');
      }

      // Return 0.0 as fallback to prevent app crash
      return 0.0;
    } finally {
      print('🚚 ==========================================');
    }
  }

  /// Create order directly via WooCommerce REST API (bypassing library flaw)
  /// This function bypasses the woocommerce_flutter_api library bug that sends orders to incorrect URLs
  Future<Map<String, dynamic>?> createOrderDirectly(Map<String, dynamic> order) async {
    print('🛒 ===== DIRECT ORDER CREATION API CALL =====');
    print('🔍 Bypassing woocommerce_flutter_api library to fix 400 Bad Request');

    // Task 2.1: Construct the correct URL
    const String correctOrderApiUrl = 'https://velvete.ly/wp-json/wc/v3/orders';
    print('🔍 Correct API URL: $correctOrderApiUrl');

    // Task 2.2: The order parameter is already the correctly formed payload
    print('🔍 Order payload size: ${order.length} fields');
    print('🔍 Order payload keys: ${order.keys.toList()}');

    try {
      // Task 2.3: Use existing authentication pattern from _wooCommerce.dio
      // The _wooCommerce.dio instance already has the correct authentication configured
      print('🔍 Using existing WooCommerce authentication:');
      print('   Consumer Key: ${_wooCommerce.username.substring(0, 10)}...');
      print('   Consumer Secret: ${_wooCommerce.password.substring(0, 10)}...');

      // FINAL INVESTIGATION - Task 2.1: Verify Final Payload Structure in Log
      print('🔍 ===== FINAL ORDER PAYLOAD TO API (BEFORE POST) =====');
      print('🔍 Full Payload: $order'); // Log the entire map as well

      print('🔍 Payload verification (NEW ORDER CREATION STRUCTURE):');
      print('  Root level id: ${order['id']} (present: ${order.containsKey('id')})'); // Should be absent
      print('  Root level number: ${order['number']} (present: ${order.containsKey('number')})'); // Should be absent
      print('  Root level order_key: ${order['order_key']} (present: ${order.containsKey('order_key')})'); // Should be absent
      print('  billing.state: ${order['billing']?['state']} (${order['billing']?['state'].runtimeType})');
      print('  shipping.state: ${order['shipping']?['state']} (${order['shipping']?['state'].runtimeType})');

      if (order.containsKey('line_items') && (order['line_items'] as List).isNotEmpty) {
        var firstLineItem = (order['line_items'] as List)[0] as Map<String, dynamic>;
        print('  line_items[0].product_id: ${firstLineItem['product_id']} (${firstLineItem['product_id'].runtimeType})'); // Should be present
        print('  line_items[0].id: ${firstLineItem['id']} (present: ${firstLineItem.containsKey('id')})'); // Should be absent
        print('  line_items[0].sku: ${firstLineItem['sku']} (${firstLineItem['sku'].runtimeType})');
        print('  line_items[0].subtotal_tax: ${firstLineItem['subtotal_tax']} (${firstLineItem['subtotal_tax'].runtimeType})');
        print('  line_items[0].tax_class: ${firstLineItem['tax_class']} (${firstLineItem['tax_class'].runtimeType})');
      }

      if (order.containsKey('shipping_lines') && (order['shipping_lines'] as List).isNotEmpty) {
        var firstShippingLine = (order['shipping_lines'] as List)[0] as Map<String, dynamic>;
        print('  shipping_lines[0].id: ${firstShippingLine['id']} (present: ${firstShippingLine.containsKey('id')})'); // Should be absent
        print('  shipping_lines[0].total: ${firstShippingLine['total']} (${firstShippingLine['total'].runtimeType})');
        print('  shipping_lines[0].total_tax: ${firstShippingLine['total_tax']} (${firstShippingLine['total_tax'].runtimeType})');
      }
      print('🔍 =====================================================');

      // Task 2.4: Execute the request using the authenticated dio instance
      final response = await _wooCommerce.dio.post(
        '/orders', // Use relative path since _wooCommerce.dio has base URL configured
        data: order,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      // Task 2.5: Handle the response
      if (response.statusCode == 201) {
        print('✅ SUCCESS: Order created successfully!');
        print('   Status Code: ${response.statusCode}');
        print('   Order ID: ${response.data['id']}');
        print('   Order Number: ${response.data['number']}');
        print('   Order Status: ${response.data['status']}');
        print('   Order Total: ${response.data['total']}');
        print('🛒 ==========================================');

        return response.data;
      } else {
        print('⚠️ UNEXPECTED: Non-201 status code: ${response.statusCode}');
        print('   Response data: ${response.data}');
        return null;
      }

    } on DioException catch (e) {
      print('❌ FAILED: Direct order creation failed with DioException');
      print('   Status Code: ${e.response?.statusCode}');
      print('   Response Data: ${e.response?.data}');
      print('   Request URL: ${e.requestOptions.uri}');
      print('   Request Method: ${e.requestOptions.method}');
      print('   Request Headers: ${e.requestOptions.headers}');
      print('   Error Type: ${e.type}');
      print('   Error Message: ${e.message}');
      print('🛒 ==========================================');
      rethrow;
    } catch (e) {
      print('❌ FAILED: Direct order creation failed with unexpected error: $e');
      print('🛒 ==========================================');
      rethrow;
    }
  }
}