{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10070,11033,11141,11252", "endColumns": "110,107,110,106", "endOffsets": "10176,11136,11247,11354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8664", "endColumns": "137", "endOffsets": "8797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,361,503,614,702,788,870,963,1052,1167,1279,1380,1481,1591,1719,1801,1887,2074,2170,2271,2383,2496", "endColumns": "160,144,141,110,87,85,81,92,88,114,111,100,100,109,127,81,85,186,95,100,111,112,161", "endOffsets": "211,356,498,609,697,783,865,958,1047,1162,1274,1375,1476,1586,1714,1796,1882,2069,2165,2266,2378,2491,2653"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5083,5244,5389,5531,5642,5730,5816,5898,5991,6080,6195,6307,6408,6509,6619,6747,6829,6915,7102,7198,7299,7411,7524", "endColumns": "160,144,141,110,87,85,81,92,88,114,111,100,100,109,127,81,85,186,95,100,111,112,161", "endOffsets": "5239,5384,5526,5637,5725,5811,5893,5986,6075,6190,6302,6403,6504,6614,6742,6824,6910,7097,7193,7294,7406,7519,7681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "19066", "endColumns": "91", "endOffsets": "19153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,18556", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,18634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "203,204", "startColumns": "4,4", "startOffsets": "19590,19678", "endColumns": "87,94", "endOffsets": "19673,19768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4819,4915,10181,10369,10541,12796,12885,17756,17844,18007,18080,18382,18466,18814,19327,19404,19473", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "4910,4997,10273,10464,10625,12880,12976,17839,17923,18075,18148,18461,18551,18886,19399,19468,19585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3080,3194", "endColumns": "113,113", "endOffsets": "3189,3303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7686,7798,7952,8082,8197,8334,8459,8564,8802,8951,9063,9216,9348,9499,9662,9726,9796", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "7793,7947,8077,8192,8329,8454,8559,8659,8946,9058,9211,9343,9494,9657,9721,9791,9875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3758,3860,3968,4070,4171,4277,4384,18965", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3855,3963,4065,4166,4272,4379,4503,19061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1098,1164,1258,1325,1387,1480,1544,1612,1675,1749,1814,1868,1989,2046,2108,2162,2241,2369,2457,2538,2636,2719,2811,2956,3036,3118,3243,3331,3413,3473,3525,3591,3666,3744,3815,3894,3967,4043,4124,4193,4313,4418,4495,4586,4679,4753,4830,4922,4979,5060,5126,5210,5296,5359,5424,5488,5557,5667,5775,5874,5980,6044,6100,6183,6280,6358", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "271,360,450,536,634,721,825,941,1032,1093,1159,1253,1320,1382,1475,1539,1607,1670,1744,1809,1863,1984,2041,2103,2157,2236,2364,2452,2533,2631,2714,2806,2951,3031,3113,3238,3326,3408,3468,3520,3586,3661,3739,3810,3889,3962,4038,4119,4188,4308,4413,4490,4581,4674,4748,4825,4917,4974,5055,5121,5205,5291,5354,5419,5483,5552,5662,5770,5869,5975,6039,6095,6178,6275,6353,6427"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3308,3397,3487,3573,3671,4508,4612,4728,10630,10691,10939,12729,12981,13043,13136,13200,13268,13331,13405,13470,13524,13645,13702,13764,13818,13897,14025,14113,14194,14292,14375,14467,14612,14692,14774,14899,14987,15069,15129,15181,15247,15322,15400,15471,15550,15623,15699,15780,15849,15969,16074,16151,16242,16335,16409,16486,16578,16635,16716,16782,16866,16952,17015,17080,17144,17213,17323,17431,17530,17636,17700,18153,18639,18736,18891", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "321,3392,3482,3568,3666,3753,4607,4723,4814,10686,10752,11028,12791,13038,13131,13195,13263,13326,13400,13465,13519,13640,13697,13759,13813,13892,14020,14108,14189,14287,14370,14462,14607,14687,14769,14894,14982,15064,15124,15176,15242,15317,15395,15466,15545,15618,15694,15775,15844,15964,16069,16146,16237,16330,16404,16481,16573,16630,16711,16777,16861,16947,17010,17075,17139,17208,17318,17426,17525,17631,17695,17751,18231,18731,18809,18960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "95,104,185,189,199,205,206", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9997,10757,17928,18236,19158,19773,19860", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "10065,10851,18002,18377,19322,19855,19939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,77", "endOffsets": "258,336"}, "to": {"startLines": "120,207", "startColumns": "4,4", "startOffsets": "12668,19944", "endColumns": "60,81", "endOffsets": "12724,20021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,208", "endColumns": "80,71,82", "endOffsets": "131,203,286"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "5002,10469,10856", "endColumns": "80,71,82", "endOffsets": "5078,10536,10934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,382,514,640,790,911,1044,1144,1299,1439", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "167,258,377,509,635,785,906,1039,1139,1294,1434,1567"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9880,10278,11359,11478,11610,11736,11886,12007,12140,12240,12395,12535", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "9992,10364,11473,11605,11731,11881,12002,12135,12235,12390,12530,12663"}}]}]}