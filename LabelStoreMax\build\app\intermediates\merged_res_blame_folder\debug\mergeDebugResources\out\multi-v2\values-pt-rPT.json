{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8645", "endColumns": "144", "endOffsets": "8785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "95,104,185,189,518,524,525", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10064,10833,18018,18324,50984,51599,51686", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "10129,10916,18092,18470,51148,51681,51762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,333,429,634,681,752,857,931,1163,1240,1347,1420,1477,1541,1612,1699,1999,2078,2145,2199,2253,2343,2475,2606,2664,2755,2838,2926,2992,3098,3381,3462,3540,3603,3664,3726,3787,3861,3947,4053,4154,4242,4341,4434,4509,4588,4674,4865,5065,5179,5309,5365,6078,6188,6253", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "170,328,424,629,676,747,852,926,1158,1235,1342,1415,1472,1536,1607,1694,1994,2073,2140,2194,2248,2338,2470,2601,2659,2750,2833,2921,2987,3093,3376,3457,3535,3598,3659,3721,3782,3856,3942,4048,4149,4237,4336,4429,4504,4583,4669,4860,5060,5174,5304,5360,6073,6183,6248,6303"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,354,355,356,357,358,364,367,372,374,375,376,377,380,382,383,387,391,392,394,396,408,433,434,435,436,437,455,462,463,464,465,467,468,469,486", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25744,25864,26022,26218,27280,27327,27398,27503,27577,27809,27886,29911,30188,30385,30687,30758,31127,31831,31910,32457,33425,33635,33725,33857,33988,34046,34637,34905,35360,35510,35616,35899,35980,36231,36411,36472,36782,37361,37435,37613,37834,39567,41996,42095,42188,42263,42342,44045,44739,44939,45053,45183,45319,46032,46142,47967", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "25859,26017,26113,26418,27322,27393,27498,27572,27804,27881,27988,29979,30240,30444,30753,30840,31422,31905,31972,32506,33474,33720,33852,33983,34041,34132,34715,34988,35421,35611,35894,35975,36053,36289,36467,36529,36838,37430,37516,37714,37930,39650,42090,42183,42258,42337,42423,44231,44934,45048,45178,45234,46027,46137,46202,48017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3293,3374,3454,3536,3635,4463,4566,4686,10709,10769,10998,12808,13063,13128,13218,13282,13350,13412,13485,13549,13603,13729,13787,13849,13903,13979,14122,14209,14289,14388,14474,14556,14695,14777,14859,14995,15082,15162,15218,15269,15335,15410,15490,15561,15640,15713,15790,15859,15933,16040,16133,16210,16303,16401,16475,16556,16655,16708,16792,16858,16947,17035,17097,17161,17224,17292,17408,17516,17623,17725,17785,18238,18736,18819,18974", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3369,3449,3531,3630,3726,4561,4681,4762,10764,10828,11085,12882,13123,13213,13277,13345,13407,13480,13544,13598,13724,13782,13844,13898,13974,14117,14204,14284,14383,14469,14551,14690,14772,14854,14990,15077,15157,15213,15264,15330,15405,15485,15556,15635,15708,15785,15854,15928,16035,16128,16205,16298,16396,16470,16551,16650,16703,16787,16853,16942,17030,17092,17156,17219,17287,17403,17511,17618,17720,17780,17835,18319,18814,18893,19048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,228,315,415,504,587,669,809,909,1022,1115,1248,1315,1432,1496,1670,1867,2014,2106,2221,2317,2416,2596,2707,2807,3067,3183,3325,3537,3755,3853,3979,4179,4282,4343,4409,4493,4596,4693,4766,4864,4948,5059,5283,5351,5420,5500,5620,5694,5782,5848,5931,6002,6070,6194,6281,6388,6485,6561,6643,6712,6804,6865,6975,7092,7197,7326,7389,7492,7603,7740,7811,7909,8015,8073,8131,8222,8330,8506,8787,9099,9196,9272,9358,9430,9546,9666,9753,9828,9901,9989,10082,10176,10320,10470,10538,10619,10673,10836,10906,10968,11047,11125,11199,11278,11422,11548,11662,11750,11826,11910,11981", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "127,223,310,410,499,582,664,804,904,1017,1110,1243,1310,1427,1491,1665,1862,2009,2101,2216,2312,2411,2591,2702,2802,3062,3178,3320,3532,3750,3848,3974,4174,4277,4338,4404,4488,4591,4688,4761,4859,4943,5054,5278,5346,5415,5495,5615,5689,5777,5843,5926,5997,6065,6189,6276,6383,6480,6556,6638,6707,6799,6860,6970,7087,7192,7321,7384,7487,7598,7735,7806,7904,8010,8068,8126,8217,8325,8501,8782,9094,9191,9267,9353,9425,9541,9661,9748,9823,9896,9984,10077,10171,10315,10465,10533,10614,10668,10831,10901,10963,11042,11120,11194,11273,11417,11543,11657,11745,11821,11905,11976,12148"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,361,379,381,384,388,389,390,393,395,397,398,399,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,460,470,471,472,473,474,475,476,477,478,487,488,489,490,491,492,493,494,495,496,497,498,499,500,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20205,20282,20378,26118,27993,28082,28165,30245,31477,31625,31738,34334,36164,36294,36534,36843,37017,37214,37521,37719,37935,38031,38130,38310,38421,38521,38781,38897,39039,39251,39469,39655,39781,39981,40084,40145,40211,40295,40398,40495,40568,40666,40750,40861,41085,41153,41222,41302,41422,41496,41584,41650,41733,41804,41872,42428,42515,42622,42719,42795,42877,42946,43038,43099,43209,43326,43431,43560,43623,43726,43837,43974,44236,44578,46207,46265,46323,46414,46522,46698,46979,47291,47388,48022,48108,48180,48296,48416,48503,48578,48651,48739,48832,48926,49070,49220,49288,49450,49504,49667,49737,49799,49878,49956,50030,50109,50253,50379,50493,50581,50657,50741,50812", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "20277,20373,20460,26213,28077,28160,28242,30380,31572,31733,31826,34462,36226,36406,36593,37012,37209,37356,37608,37829,38026,38125,38305,38416,38516,38776,38892,39034,39246,39464,39562,39776,39976,40079,40140,40206,40290,40393,40490,40563,40661,40745,40856,41080,41148,41217,41297,41417,41491,41579,41645,41728,41799,41867,41991,42510,42617,42714,42790,42872,42941,43033,43094,43204,43321,43426,43555,43618,43721,43832,43969,44040,44329,44679,46260,46318,46409,46517,46693,46974,47286,47383,47459,48103,48175,48291,48411,48498,48573,48646,48734,48827,48921,49065,49215,49283,49364,49499,49662,49732,49794,49873,49951,50025,50104,50248,50374,50488,50576,50652,50736,50807,50979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,485,545,600,680,761,845,948,1051,1137,1215,1296,1381,1482,1577,1649,1741,1829,1917,2025,2107,2199,2278,2377,2455,2555,2650,2741,2828,2935,3012,3093,3206,3312,3382,3450,4169,4864,4942,5065,5173,5228,5328,5420,5491,5588,5689,5746,5833,5884,5962,6071,6146,6220,6287,6353,6401,6487,6587,6660,6710,6757,6826,6884,6947,7154,7328,7472,7537,7628,7703,7798,7888,7983,8060,8151,8235,8341,8430,8525,8580,8719,8769,8824,8904,8976,9049,9118,9194,9285,9355,9407", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "124,207,269,343,426,480,540,595,675,756,840,943,1046,1132,1210,1291,1376,1477,1572,1644,1736,1824,1912,2020,2102,2194,2273,2372,2450,2550,2645,2736,2823,2930,3007,3088,3201,3307,3377,3445,4164,4859,4937,5060,5168,5223,5323,5415,5486,5583,5684,5741,5828,5879,5957,6066,6141,6215,6282,6348,6396,6482,6582,6655,6705,6752,6821,6879,6942,7149,7323,7467,7532,7623,7698,7793,7883,7978,8055,8146,8230,8336,8425,8520,8575,8714,8764,8819,8899,8971,9044,9113,9189,9280,9350,9402,9483"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,362,363,365,366,370,371,373,378,385,386,457,458,459,461,466,479,480,481,482,483,484,485,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19715,19789,19872,19934,20008,20091,20145,20465,20520,20600,20681,20914,21100,21203,21357,21714,22004,23042,23297,23392,23464,23624,23712,23800,23970,24279,24371,24450,24549,24627,24727,24822,24913,25000,25107,25264,25525,25638,26423,26493,26561,28247,28942,29020,29143,29251,29306,29406,29498,29569,29666,29767,29824,30449,30500,30578,30845,30920,30994,31061,31577,31977,32063,32163,32236,32286,32573,32642,32700,32763,32970,33144,33479,33544,34467,34542,34720,34810,35192,35269,35426,36058,36598,36687,44334,44389,44528,44684,45239,47464,47536,47609,47678,47754,47845,47915,49369", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "19784,19867,19929,20003,20086,20140,20200,20515,20595,20676,20760,21012,21198,21284,21430,21790,22084,23138,23387,23459,23551,23707,23795,23903,24047,24366,24445,24544,24622,24722,24817,24908,24995,25102,25179,25340,25633,25739,26488,26556,27275,28937,29015,29138,29246,29301,29401,29493,29564,29661,29762,29819,29906,30495,30573,30682,30915,30989,31056,31122,31620,32058,32158,32231,32281,32328,32637,32695,32758,32965,33139,33283,33539,33630,34537,34632,34805,34900,35264,35355,35505,36159,36682,36777,44384,44523,44573,44734,45314,47531,47604,47673,47749,47840,47910,47962,49445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "522,523", "startColumns": "4,4", "startOffsets": "51423,51510", "endColumns": "86,88", "endOffsets": "51505,51594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4767,4862,10250,10442,10623,12887,12966,17840,17931,18097,18169,18475,18560,18898,51153,51229,51301", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4857,4940,10342,10536,10704,12961,13058,17926,18013,18164,18233,18555,18645,18969,51224,51296,51418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,204,272,358,429,490,563,630,692,760,830,890,951,1031,1095,1167,1230,1309,1374,1464,1544,1631,1724,1837,1928,1978,2026,2102,2164,2232,2301,2409,2498,2598", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "116,199,267,353,424,485,558,625,687,755,825,885,946,1026,1090,1162,1225,1304,1369,1459,1539,1626,1719,1832,1923,1973,2021,2097,2159,2227,2296,2404,2493,2593,2692"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,359,360,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20765,21017,21289,21435,21521,21592,22089,22162,22302,22364,22432,22502,22562,22623,22703,22826,22898,23143,24052,24117,25184,25345,25432,29984,30097,31427,32333,32381,32511,33288,33356,34137,34245,34993,35093", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "20826,21095,21352,21516,21587,21648,22157,22224,22359,22427,22497,22557,22618,22698,22762,22893,22956,23217,24112,24202,25259,25427,25520,30092,30183,31472,32376,32452,32568,33351,33420,34240,34329,35088,35187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,18650", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,18731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10134,11090,11189,11301", "endColumns": "115,98,111,102", "endOffsets": "10245,11184,11296,11399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,152,260,327,395,461,547", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "147,255,322,390,456,542,611"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19154,19251,19359,19426,19494,19560,19646", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "19246,19354,19421,19489,19555,19641,19710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,214", "endColumns": "76,81,76", "endOffsets": "127,209,286"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4945,10541,10921", "endColumns": "76,81,76", "endOffsets": "5017,10618,10993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3731,3828,3930,4029,4129,4236,4342,19053", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3823,3925,4024,4124,4231,4337,4458,19149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,354,501,612,697,783,861,952,1049,1165,1282,1381,1480,1591,1719,1801,1887,2073,2167,2268,2383,2499", "endColumns": "157,140,146,110,84,85,77,90,96,115,116,98,98,110,127,81,85,185,93,100,114,115,147", "endOffsets": "208,349,496,607,692,778,856,947,1044,1160,1277,1376,1475,1586,1714,1796,1882,2068,2162,2263,2378,2494,2642"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5022,5180,5321,5468,5579,5664,5750,5828,5919,6016,6132,6249,6348,6447,6558,6686,6768,6854,7040,7134,7235,7350,7466", "endColumns": "157,140,146,110,84,85,77,90,96,115,116,98,98,110,127,81,85,185,93,100,114,115,147", "endOffsets": "5175,5316,5463,5574,5659,5745,5823,5914,6011,6127,6244,6343,6442,6553,6681,6763,6849,7035,7129,7230,7345,7461,7609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "120,526", "startColumns": "4,4", "startOffsets": "12747,51767", "endColumns": "60,79", "endOffsets": "12803,51842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3054,3171", "endColumns": "116,121", "endOffsets": "3166,3288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,266,395,534,681,816,945,1092,1194,1334,1483", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "166,261,390,529,676,811,940,1087,1189,1329,1478,1604"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9948,10347,11404,11533,11672,11819,11954,12083,12230,12332,12472,12621", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "10059,10437,11528,11667,11814,11949,12078,12225,12327,12467,12616,12742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7614,7719,7882,8010,8118,8286,8414,8536,8790,8978,9086,9256,9387,9546,9724,9792,9861", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "7714,7877,8005,8113,8281,8409,8531,8640,8973,9081,9251,9382,9541,9719,9787,9856,9943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,199,261,343,408,481,540,621,696,764,826", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "133,194,256,338,403,476,535,616,691,759,821,893"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20831,21653,21795,21857,21939,22229,22767,22961,23222,23556,23908,24207", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "20909,21709,21852,21934,21999,22297,22821,23037,23292,23619,23965,24274"}}]}]}