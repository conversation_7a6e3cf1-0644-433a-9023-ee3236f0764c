{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8488", "endColumns": "131", "endOffsets": "8615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3662,3757,3859,3956,4053,4159,4277,18498", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3752,3854,3951,4048,4154,4272,4387,18594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,513,514,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4697,4787,9959,10155,10327,12514,12597,17334,17421,17586,17656,17939,18021,18348,49972,50050,50116", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "4782,4864,10052,10250,10408,12592,12683,17416,17501,17651,17718,18016,18099,18415,50045,50111,50230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "50235,50321", "endColumns": "85,89", "endOffsets": "50316,50406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,222,304,404,495,577,658,801,901,1007,1100,1220,1280,1392,1459,1669,1901,2034,2126,2235,2328,2427,2605,2713,2806,3084,3192,3320,3528,3740,3837,3949,4166,4260,4320,4390,4478,4579,4671,4744,4840,4924,5030,5266,5332,5400,5478,5594,5678,5767,5835,5916,5987,6063,6193,6280,6384,6478,6550,6629,6696,6784,6844,6948,7068,7175,7304,7367,7461,7565,7688,7759,7851,7962,8022,8090,8168,8272,8450,8728,9032,9132,9206,9296,9369,9487,9604,9689,9761,9832,9918,10003,10089,10228,10371,10439,10511,10575,10763,10834,10892,10968,11043,11120,11201,11319,11438,11543,11630,11708,11797,11866", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "126,217,299,399,490,572,653,796,896,1002,1095,1215,1275,1387,1454,1664,1896,2029,2121,2230,2323,2422,2600,2708,2801,3079,3187,3315,3523,3735,3832,3944,4161,4255,4315,4385,4473,4574,4666,4739,4835,4919,5025,5261,5327,5395,5473,5589,5673,5762,5830,5911,5982,6058,6188,6275,6379,6473,6545,6624,6691,6779,6839,6943,7063,7170,7299,7362,7456,7560,7683,7754,7846,7957,8017,8085,8163,8267,8445,8723,9027,9127,9201,9291,9364,9482,9599,9684,9756,9827,9913,9998,10084,10223,10366,10434,10506,10570,10758,10829,10887,10963,11038,11115,11196,11314,11433,11538,11625,11703,11792,11861,12023"}, "to": {"startLines": "205,206,207,277,289,290,291,308,321,323,324,355,373,375,378,382,383,384,387,389,391,392,393,394,395,396,397,398,399,400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,450,454,464,465,466,467,468,469,470,471,472,481,482,483,484,485,486,487,488,489,490,491,492,493,494,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19088,19164,19255,24950,26872,26963,27045,29090,30327,30474,30580,33173,35008,35132,35366,35669,35879,36111,36399,36591,36795,36888,36987,37165,37273,37366,37644,37752,37880,38088,38300,38496,38608,38825,38919,38979,39049,39137,39238,39330,39403,39499,39583,39689,39925,39991,40059,40137,40253,40337,40426,40494,40575,40646,40722,41284,41371,41475,41569,41641,41720,41787,41875,41935,42039,42159,42266,42395,42458,42552,42656,42779,43079,43401,45095,45155,45223,45301,45405,45583,45861,46165,46265,46896,46986,47059,47177,47294,47379,47451,47522,47608,47693,47779,47918,48061,48129,48286,48350,48538,48609,48667,48743,48818,48895,48976,49094,49213,49318,49405,49483,49572,49641", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "19159,19250,19332,25045,26958,27040,27121,29228,30422,30575,30668,33288,35063,35239,35428,35874,36106,36239,36486,36695,36883,36982,37160,37268,37361,37639,37747,37875,38083,38295,38392,38603,38820,38914,38974,39044,39132,39233,39325,39398,39494,39578,39684,39920,39986,40054,40132,40248,40332,40421,40489,40570,40641,40717,40847,41366,41470,41564,41636,41715,41782,41870,41930,42034,42154,42261,42390,42453,42547,42651,42774,42845,43166,43507,45150,45218,45296,45400,45578,45856,46160,46260,46334,46981,47054,47172,47289,47374,47446,47517,47603,47688,47774,47913,48056,48124,48196,48345,48533,48604,48662,48738,48813,48890,48971,49089,49208,49313,49400,49478,49567,49636,49798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,195,259,346,409,481,540,616,686,753,822", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "131,190,254,341,404,476,535,611,681,748,817,884"}, "to": {"startLines": "213,223,225,226,227,231,239,242,245,249,253,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19696,20503,20641,20705,20792,21075,21606,21805,22050,22364,22736,23048", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "19772,20557,20700,20787,20850,21142,21660,21876,22115,22426,22800,23110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3026,3135", "endColumns": "108,117", "endOffsets": "3130,3248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7482,7589,7753,7879,7985,8140,8267,8382,8620,8786,8891,9055,9181,9336,9480,9544,9604", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "7584,7748,7874,7980,8135,8262,8377,8483,8781,8886,9050,9176,9331,9475,9539,9599,9678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,341,427,485,544,600,676,759,839,940,1041,1125,1203,1282,1365,1461,1551,1618,1705,1792,1891,2010,2099,2186,2264,2364,2440,2533,2623,2709,2793,2905,2978,3064,3182,3293,3368,3432,4171,4886,4963,5085,5189,5244,5344,5439,5505,5597,5690,5747,5831,5882,5966,6065,6133,6204,6271,6337,6384,6465,6560,6635,6683,6728,6805,6863,6929,7113,7287,7407,7472,7555,7633,7731,7816,7901,7978,8072,8148,8254,8341,8430,8483,8612,8660,8714,8779,8854,8922,8990,9064,9152,9220,9271", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "119,197,259,336,422,480,539,595,671,754,834,935,1036,1120,1198,1277,1360,1456,1546,1613,1700,1787,1886,2005,2094,2181,2259,2359,2435,2528,2618,2704,2788,2900,2973,3059,3177,3288,3363,3427,4166,4881,4958,5080,5184,5239,5339,5434,5500,5592,5685,5742,5826,5877,5961,6060,6128,6199,6266,6332,6379,6460,6555,6630,6678,6723,6800,6858,6924,7108,7282,7402,7467,7550,7628,7726,7811,7896,7973,8067,8143,8249,8336,8425,8478,8607,8655,8709,8774,8849,8917,8985,9059,9147,9215,9266,9351"}, "to": {"startLines": "198,199,200,201,202,203,204,208,209,210,211,214,216,217,219,224,228,243,246,247,248,250,251,252,254,258,259,260,261,262,263,264,265,266,267,269,272,273,279,280,281,292,293,294,295,296,297,298,299,300,301,302,303,310,311,312,315,316,317,318,322,327,328,329,330,331,336,337,338,339,340,341,345,346,356,357,359,360,364,365,367,372,379,380,451,452,453,455,460,473,474,475,476,477,478,479,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18599,18668,18746,18808,18885,18971,19029,19337,19393,19469,19552,19777,19959,20060,20211,20562,20855,21881,22120,22210,22277,22431,22518,22617,22805,23115,23202,23280,23380,23456,23549,23639,23725,23809,23921,24069,24329,24447,25291,25366,25430,27126,27841,27918,28040,28144,28199,28299,28394,28460,28552,28645,28702,29297,29348,29432,29699,29767,29838,29905,30427,30812,30893,30988,31063,31111,31396,31473,31531,31597,31781,31955,32263,32328,33293,33371,33550,33635,34001,34078,34251,34902,35433,35520,43171,43224,43353,43512,44110,46339,46414,46482,46550,46624,46712,46780,48201", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "18663,18741,18803,18880,18966,19024,19083,19388,19464,19547,19627,19873,20055,20139,20284,20636,20933,21972,22205,22272,22359,22513,22612,22731,22889,23197,23275,23375,23451,23544,23634,23720,23804,23916,23989,24150,24442,24553,25361,25425,26164,27836,27913,28035,28139,28194,28294,28389,28455,28547,28640,28697,28781,29343,29427,29526,29762,29833,29900,29966,30469,30888,30983,31058,31106,31151,31468,31526,31592,31776,31950,32070,32323,32406,33366,33464,33630,33715,34073,34167,34322,35003,35515,35604,43219,43348,43396,43561,44170,46409,46477,46545,46619,46707,46775,46826,48281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,205", "endColumns": "77,71,76", "endOffsets": "128,200,277"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4869,10255,10629", "endColumns": "77,71,76", "endOffsets": "4942,10322,10701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,18104", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,18184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,354,490,614,695,780,854,942,1030,1147,1256,1346,1436,1537,1657,1738,1822,2011,2107,2209,2327,2436", "endColumns": "164,133,135,123,80,84,73,87,87,116,108,89,89,100,119,80,83,188,95,101,117,108,153", "endOffsets": "215,349,485,609,690,775,849,937,1025,1142,1251,1341,1431,1532,1652,1733,1817,2006,2102,2204,2322,2431,2585"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4947,5112,5246,5382,5506,5587,5672,5746,5834,5922,6039,6148,6238,6328,6429,6549,6630,6714,6903,6999,7101,7219,7328", "endColumns": "164,133,135,123,80,84,73,87,87,116,108,89,89,100,119,80,83,188,95,101,117,108,153", "endOffsets": "5107,5241,5377,5501,5582,5667,5741,5829,5917,6034,6143,6233,6323,6424,6544,6625,6709,6898,6994,7096,7214,7323,7477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,344,447,688,736,806,907,980,1240,1300,1391,1457,1512,1576,1652,1744,2053,2125,2192,2245,2298,2388,2521,2629,2686,2772,2853,2939,3018,3124,3437,3516,3593,3657,3717,3779,3839,3912,3994,4094,4189,4288,4388,4482,4556,4635,4720,4949,5187,5303,5435,5493,6247,6351,6413", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "170,339,442,683,731,801,902,975,1235,1295,1386,1452,1507,1571,1647,1739,2048,2120,2187,2240,2293,2383,2516,2624,2681,2767,2848,2934,3013,3119,3432,3511,3588,3652,3712,3774,3834,3907,3989,4089,4184,4283,4383,4477,4551,4630,4715,4944,5182,5298,5430,5488,6242,6346,6408,6473"}, "to": {"startLines": "274,275,276,278,282,283,284,285,286,287,288,304,307,309,313,314,319,325,326,334,344,348,349,350,351,352,358,361,366,368,369,370,371,374,376,377,381,385,386,388,390,402,427,428,429,430,431,449,456,457,458,459,461,462,463,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24558,24678,24847,25050,26169,26217,26287,26388,26461,26721,26781,28786,29035,29233,29531,29607,29971,30673,30745,31279,32210,32508,32598,32731,32839,32896,33469,33720,34172,34327,34433,34746,34825,35068,35244,35304,35609,36244,36317,36491,36700,38397,40852,40952,41046,41120,41199,42850,43566,43804,43920,44052,44175,44929,45033,46831", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "24673,24842,24945,25286,26212,26282,26383,26456,26716,26776,26867,28847,29085,29292,29602,29694,30275,30740,30807,31327,32258,32593,32726,32834,32891,32977,33545,33801,34246,34428,34741,34820,34897,35127,35299,35361,35664,36312,36394,36586,36790,38491,40947,41041,41115,41194,41279,43074,43799,43915,44047,44105,44924,45028,45090,46891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "32411", "endColumns": "96", "endOffsets": "32503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9859,10800,10898,11007", "endColumns": "99,97,108,100", "endOffsets": "9954,10893,11002,11103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,351,420,481,552,618,680,748,818,878,940,1013,1077,1154,1217,1290,1353,1444,1519,1601,1693,1791,1876,1923,1970,2046,2110,2176,2245,2349,2436,2534", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "114,195,262,346,415,476,547,613,675,743,813,873,935,1008,1072,1149,1212,1285,1348,1439,1514,1596,1688,1786,1871,1918,1965,2041,2105,2171,2240,2344,2431,2529,2626"}, "to": {"startLines": "212,215,218,220,221,222,229,230,232,233,234,235,236,237,238,240,241,244,255,256,268,270,271,305,306,320,332,333,335,342,343,353,354,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19632,19878,20144,20289,20373,20442,20938,21009,21147,21209,21277,21347,21407,21469,21542,21665,21742,21977,22894,22957,23994,24155,24237,28852,28950,30280,31156,31203,31332,32075,32141,32982,33086,33806,33904", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "19691,19954,20206,20368,20437,20498,21004,21070,21204,21272,21342,21402,21464,21537,21601,21737,21800,22045,22952,23043,24064,24232,24324,28945,29030,30322,31198,31274,31391,32136,32205,33081,33168,33899,33996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,260,375,504,642,776,905,1040,1140,1279,1416", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "157,255,370,499,637,771,900,1035,1135,1274,1411,1535"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9683,10057,11108,11223,11352,11490,11624,11753,11888,11988,12127,12264", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "9785,10150,11218,11347,11485,11619,11748,11883,11983,12122,12259,12383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3253,3332,3408,3487,3577,4392,4498,4614,10413,10477,10706,12449,12688,12747,12834,12896,12958,13018,13084,13146,13200,13312,13369,13430,13484,13556,13682,13768,13846,13939,14025,14109,14248,14329,14410,14545,14635,14717,14770,14822,14888,14960,15044,15115,15195,15270,15346,15419,15494,15592,15677,15752,15844,15938,16012,16085,16179,16231,16313,16382,16467,16554,16616,16680,16743,16815,16918,17023,17118,17221,17278,17723,18189,18270,18420", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3327,3403,3482,3572,3657,4493,4609,4692,10472,10537,10795,12509,12742,12829,12891,12953,13013,13079,13141,13195,13307,13364,13425,13479,13551,13677,13763,13841,13934,14020,14104,14243,14324,14405,14540,14630,14712,14765,14817,14883,14955,15039,15110,15190,15265,15341,15414,15489,15587,15672,15747,15839,15933,16007,16080,16174,16226,16308,16377,16462,16549,16611,16675,16738,16810,16913,17018,17113,17216,17273,17329,17798,18265,18343,18493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "120,520", "startColumns": "4,4", "startOffsets": "12388,50575", "endColumns": "60,77", "endOffsets": "12444,50648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "95,104,185,189,512,518,519", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9790,10542,17506,17803,49803,50411,50496", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "9854,10624,17581,17934,49967,50491,50570"}}]}]}