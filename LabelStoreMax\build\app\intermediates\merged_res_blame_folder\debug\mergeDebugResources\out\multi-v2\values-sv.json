{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,817,930,1007,1082,1175,1270,1365,1459,1561,1656,1753,1851,1947,2040,2120,2226,2325,2421,2526,2629,2731,2885,18004", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,812,925,1002,1077,1170,1265,1360,1454,1556,1651,1748,1846,1942,2035,2115,2221,2320,2416,2521,2624,2726,2880,2982,18079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,353,491,601,684,770,847,936,1027,1140,1249,1341,1433,1533,1648,1730,1811,1992,2089,2188,2301,2410", "endColumns": "152,144,137,109,82,85,76,88,90,112,108,91,91,99,114,81,80,180,96,98,112,108,136", "endOffsets": "203,348,486,596,679,765,842,931,1022,1135,1244,1336,1428,1528,1643,1725,1806,1987,2084,2183,2296,2405,2542"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4954,5107,5252,5390,5500,5583,5669,5746,5835,5926,6039,6148,6240,6332,6432,6547,6629,6710,6891,6988,7087,7200,7309", "endColumns": "152,144,137,109,82,85,76,88,90,112,108,91,91,99,114,81,80,180,96,98,112,108,136", "endOffsets": "5102,5247,5385,5495,5578,5664,5741,5830,5921,6034,6143,6235,6327,6427,6542,6624,6705,6886,6983,7082,7195,7304,7441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,342,479,648,727", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "171,259,337,474,643,722,798"}, "to": {"startLines": "95,104,185,189,512,518,519", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9761,10499,17407,17691,48751,49364,49443", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "9827,10582,17480,17823,48915,49438,49514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "31903", "endColumns": "84", "endOffsets": "31983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,337,434,639,688,755,851,920,1177,1254,1360,1430,1484,1548,1625,1718,2025,2099,2165,2218,2271,2353,2470,2577,2634,2720,2798,2880,2946,3045,3323,3401,3484,3547,3607,3670,3730,3802,3883,3983,4083,4167,4265,4357,4430,4509,4594,4787,4989,5102,5225,5282,5983,6085,6151", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "163,332,429,634,683,750,846,915,1172,1249,1355,1425,1479,1543,1620,1713,2020,2094,2160,2213,2266,2348,2465,2572,2629,2715,2793,2875,2941,3040,3318,3396,3479,3542,3602,3665,3725,3797,3878,3978,4078,4162,4260,4352,4425,4504,4589,4782,4984,5097,5220,5277,5978,6080,6146,6201"}, "to": {"startLines": "274,275,276,278,282,283,284,285,286,287,288,304,307,309,313,314,319,325,326,334,344,348,349,350,351,352,358,361,366,368,369,370,371,374,376,377,381,385,386,388,390,402,427,428,429,430,431,449,456,457,458,459,461,462,463,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24296,24409,24578,24768,25767,25816,25883,25979,26048,26305,26382,28287,28549,28728,29011,29088,29459,30158,30232,30777,31698,31988,32070,32187,32294,32351,32904,33141,33562,33709,33808,34086,34164,34414,34595,34655,34957,35543,35615,35788,35993,37653,40019,40117,40209,40282,40361,41994,42674,42876,42989,43112,43236,43937,44039,45824", "endColumns": "112,168,96,204,48,66,95,68,256,76,105,69,53,63,76,92,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,71,80,99,99,83,97,91,72,78,84,192,201,112,122,56,700,101,65,54", "endOffsets": "24404,24573,24670,24968,25811,25878,25974,26043,26300,26377,26483,28352,28598,28787,29083,29176,29761,30227,30293,30825,31746,32065,32182,32289,32346,32432,32977,33218,33623,33803,34081,34159,34242,34472,34650,34713,35012,35610,35691,35883,36088,37732,40112,40204,40277,40356,40441,42182,42871,42984,43107,43164,43932,44034,44100,45874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2987,3099", "endColumns": "111,112", "endOffsets": "3094,3207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,343,429,486,549,601,667,748,817,912,1007,1091,1169,1247,1330,1426,1518,1587,1676,1762,1849,1956,2039,2128,2204,2300,2372,2461,2553,2636,2710,2805,2869,2940,3052,3157,3228,3293,3951,4590,4664,4776,4877,4932,5029,5118,5184,5272,5365,5422,5506,5555,5631,5725,5792,5870,5937,6003,6050,6133,6230,6306,6351,6400,6467,6525,6593,6773,6928,7064,7129,7216,7291,7378,7457,7537,7614,7701,7782,7887,7974,8061,8114,8246,8296,8358,8425,8492,8565,8629,8701,8784,8852,8904", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "118,195,261,338,424,481,544,596,662,743,812,907,1002,1086,1164,1242,1325,1421,1513,1582,1671,1757,1844,1951,2034,2123,2199,2295,2367,2456,2548,2631,2705,2800,2864,2935,3047,3152,3223,3288,3946,4585,4659,4771,4872,4927,5024,5113,5179,5267,5360,5417,5501,5550,5626,5720,5787,5865,5932,5998,6045,6128,6225,6301,6346,6395,6462,6520,6588,6768,6923,7059,7124,7211,7286,7373,7452,7532,7609,7696,7777,7882,7969,8056,8109,8241,8291,8353,8420,8487,8560,8624,8696,8779,8847,8899,8978"}, "to": {"startLines": "198,199,200,201,202,203,204,208,209,210,211,214,216,217,219,224,228,243,246,247,248,250,251,252,254,258,259,260,261,262,263,264,265,266,267,269,272,273,279,280,281,292,293,294,295,296,297,298,299,300,301,302,303,310,311,312,315,316,317,318,322,327,328,329,330,331,336,337,338,339,340,341,345,346,356,357,359,360,364,365,367,372,379,380,451,452,453,455,460,473,474,475,476,477,478,479,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18502,18570,18647,18713,18790,18876,18933,19241,19293,19359,19440,19648,19818,19913,20064,20415,20705,21741,21984,22076,22145,22300,22386,22473,22643,22940,23029,23105,23201,23273,23362,23454,23537,23611,23706,23844,24079,24191,24973,25044,25109,26732,27371,27445,27557,27658,27713,27810,27899,27965,28053,28146,28203,28792,28841,28917,29181,29248,29326,29393,29907,30298,30381,30478,30554,30599,30892,30959,31017,31085,31265,31420,31751,31816,32742,32817,32982,33061,33398,33475,33628,34247,34783,34870,42277,42330,42462,42612,43169,45345,45412,45485,45549,45621,45704,45772,47171", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,104,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,66,65,46,82,96,75,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,104,86,86,52,131,49,61,66,66,72,63,71,82,67,51,78", "endOffsets": "18565,18642,18708,18785,18871,18928,18991,19288,19354,19435,19504,19738,19908,19992,20137,20488,20783,21832,22071,22140,22229,22381,22468,22575,22721,23024,23100,23196,23268,23357,23449,23532,23606,23701,23765,23910,24186,24291,25039,25104,25762,27366,27440,27552,27653,27708,27805,27894,27960,28048,28141,28198,28282,28836,28912,29006,29243,29321,29388,29454,29949,30376,30473,30549,30594,30643,30954,31012,31080,31260,31415,31551,31811,31898,32812,32899,33056,33136,33470,33557,33704,34347,34865,34952,42325,42457,42507,42669,43231,45407,45480,45544,45616,45699,45767,45819,47245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,188,250,335,400,477,536,612,684,750,813", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "125,183,245,330,395,472,531,607,679,745,808,877"}, "to": {"startLines": "213,223,225,226,227,231,239,242,245,249,253,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19573,20357,20493,20555,20640,20927,21472,21665,21912,22234,22580,22871", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "19643,20410,20550,20635,20700,20999,21526,21736,21979,22295,22638,22935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3666,3761,3863,3961,4060,4168,4273,18401", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3756,3858,3956,4055,4163,4268,4389,18497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,194,261,346,415,476,548,615,679,747,817,877,935,1008,1083,1154,1217,1292,1355,1437,1511,1590,1675,1782,1867,1915,1963,2044,2106,2179,2248,2345,2432,2521", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "114,189,256,341,410,471,543,610,674,742,812,872,930,1003,1078,1149,1212,1287,1350,1432,1506,1585,1670,1777,1862,1910,1958,2039,2101,2174,2243,2340,2427,2516,2602"}, "to": {"startLines": "212,215,218,220,221,222,229,230,232,233,234,235,236,237,238,240,241,244,255,256,268,270,271,305,306,320,332,333,335,342,343,353,354,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19509,19743,19997,20142,20227,20296,20788,20860,21004,21068,21136,21206,21266,21324,21397,21531,21602,21837,22726,22789,23770,23915,23994,28357,28464,29766,30648,30696,30830,31556,31629,32437,32534,33223,33312", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "19568,19813,20059,20222,20291,20352,20855,20922,21063,21131,21201,21261,21319,21392,21467,21597,21660,21907,22784,22866,23839,23989,24074,28459,28544,29809,30691,30772,30887,31624,31693,32529,32616,33307,33393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,513,514,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4693,4786,9932,10114,10289,12502,12578,17237,17326,17485,17549,17828,17914,18244,48920,48998,49065", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4781,4869,10023,10208,10372,12573,12661,17321,17402,17544,17608,17909,17999,18308,48993,49060,49180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "49185,49275", "endColumns": "89,88", "endOffsets": "49270,49359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9832,10753,10853,10966", "endColumns": "99,99,112,97", "endOffsets": "9927,10848,10961,11059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,217,300,393,482,560,637,762,855,964,1059,1180,1242,1360,1425,1614,1817,1951,2043,2148,2240,2336,2524,2623,2720,2974,3078,3201,3397,3613,3708,3818,4013,4102,4162,4227,4313,4405,4503,4576,4673,4763,4870,5083,5153,5220,5294,5402,5494,5585,5653,5737,5809,5878,5990,6078,6180,6272,6345,6425,6494,6582,6641,6743,6847,6951,7082,7143,7234,7334,7466,7538,7628,7728,7783,7845,7923,8025,8207,8491,8795,8893,8968,9058,9130,9240,9356,9449,9529,9606,9691,9771,9861,9981,10120,10187,10260,10314,10495,10565,10624,10697,10772,10850,10933,11052,11177,11287,11372,11451,11533,11602", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "122,212,295,388,477,555,632,757,850,959,1054,1175,1237,1355,1420,1609,1812,1946,2038,2143,2235,2331,2519,2618,2715,2969,3073,3196,3392,3608,3703,3813,4008,4097,4157,4222,4308,4400,4498,4571,4668,4758,4865,5078,5148,5215,5289,5397,5489,5580,5648,5732,5804,5873,5985,6073,6175,6267,6340,6420,6489,6577,6636,6738,6842,6946,7077,7138,7229,7329,7461,7533,7623,7723,7778,7840,7918,8020,8202,8486,8790,8888,8963,9053,9125,9235,9351,9444,9524,9601,9686,9766,9856,9976,10115,10182,10255,10309,10490,10560,10619,10692,10767,10845,10928,11047,11172,11282,11367,11446,11528,11597,11756"}, "to": {"startLines": "205,206,207,277,289,290,291,308,321,323,324,355,373,375,378,382,383,384,387,389,391,392,393,394,395,396,397,398,399,400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,450,454,464,465,466,467,468,469,470,471,472,481,482,483,484,485,486,487,488,489,490,491,492,493,494,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18996,19068,19158,24675,26488,26577,26655,28603,29814,29954,30063,32621,34352,34477,34718,35017,35206,35409,35696,35888,36093,36185,36281,36469,36568,36665,36919,37023,37146,37342,37558,37737,37847,38042,38131,38191,38256,38342,38434,38532,38605,38702,38792,38899,39112,39182,39249,39323,39431,39523,39614,39682,39766,39838,39907,40446,40534,40636,40728,40801,40881,40950,41038,41097,41199,41303,41407,41538,41599,41690,41790,41922,42187,42512,44105,44160,44222,44300,44402,44584,44868,45172,45270,45879,45969,46041,46151,46267,46360,46440,46517,46602,46682,46772,46892,47031,47098,47250,47304,47485,47555,47614,47687,47762,47840,47923,48042,48167,48277,48362,48441,48523,48592", "endColumns": "71,89,82,92,88,77,76,124,92,108,94,120,61,117,64,188,202,133,91,104,91,95,187,98,96,253,103,122,195,215,94,109,194,88,59,64,85,91,97,72,96,89,106,212,69,66,73,107,91,90,67,83,71,68,111,87,101,91,72,79,68,87,58,101,103,103,130,60,90,99,131,71,89,99,54,61,77,101,181,283,303,97,74,89,71,109,115,92,79,76,84,79,89,119,138,66,72,53,180,69,58,72,74,77,82,118,124,109,84,78,81,68,158", "endOffsets": "19063,19153,19236,24763,26572,26650,26727,28723,29902,30058,30153,32737,34409,34590,34778,35201,35404,35538,35783,35988,36180,36276,36464,36563,36660,36914,37018,37141,37337,37553,37648,37842,38037,38126,38186,38251,38337,38429,38527,38600,38697,38787,38894,39107,39177,39244,39318,39426,39518,39609,39677,39761,39833,39902,40014,40529,40631,40723,40796,40876,40945,41033,41092,41194,41298,41402,41533,41594,41685,41785,41917,41989,42272,42607,44155,44217,44295,44397,44579,44863,45167,45265,45340,45964,46036,46146,46262,46355,46435,46512,46597,46677,46767,46887,47026,47093,47166,47299,47480,47550,47609,47682,47757,47835,47918,48037,48162,48272,48357,48436,48518,48587,48746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3212,3311,3403,3484,3586,4394,4492,4614,10377,10436,10661,12438,12666,12726,12818,12883,12946,13008,13075,13139,13193,13298,13357,13418,13472,13541,13660,13743,13820,13910,13994,14078,14214,14293,14377,14499,14585,14663,14717,14768,14834,14903,14977,15048,15124,15196,15273,15344,15418,15529,15620,15699,15786,15874,15946,16020,16105,16156,16235,16302,16383,16467,16529,16593,16656,16724,16831,16930,17029,17124,17182,17613,18084,18165,18313", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "311,3306,3398,3479,3581,3661,4487,4609,4688,10431,10494,10748,12497,12721,12813,12878,12941,13003,13070,13134,13188,13293,13352,13413,13467,13536,13655,13738,13815,13905,13989,14073,14209,14288,14372,14494,14580,14658,14712,14763,14829,14898,14972,15043,15119,15191,15268,15339,15413,15524,15615,15694,15781,15869,15941,16015,16100,16151,16230,16297,16378,16462,16524,16588,16651,16719,16826,16925,17024,17119,17177,17232,17686,18160,18239,18396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7446,7553,7710,7837,7947,8088,8213,8336,8588,8736,8844,9006,9134,9288,9444,9510,9573", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "7548,7705,7832,7942,8083,8208,8331,8435,8731,8839,9001,9129,9283,9439,9505,9568,9647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "120,520", "startColumns": "4,4", "startOffsets": "12377,49519", "endColumns": "60,73", "endOffsets": "12433,49588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8440", "endColumns": "147", "endOffsets": "8583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9652,10028,11064,11185,11324,11454,11576,11705,11841,11946,12098,12250", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "9756,10109,11180,11319,11449,11571,11700,11836,11941,12093,12245,12372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,211", "endColumns": "79,75,73", "endOffsets": "130,206,280"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4874,10213,10587", "endColumns": "79,75,73", "endOffsets": "4949,10284,10656"}}]}]}