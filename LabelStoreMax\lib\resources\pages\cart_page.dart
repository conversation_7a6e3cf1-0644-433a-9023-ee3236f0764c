// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/material.dart';
import '/app/events/cart_remove_all_event.dart';
import '/app/services/auth_service.dart';
import '/resources/widgets/cart_product_item_widget.dart';
import '/resources/widgets/shopping_cart_total_widget.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/pages/checkout_confirmation_page.dart';
import '/resources/pages/home_page.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/resources/widgets/account_detail_orders_widget.dart';

class CartPage extends NyStatefulWidget {
  static RouteView path = ("/cart", (_) => CartPage());

  CartPage({super.key}) : super(child: () => _CartPageState());
}

class _CartPageState extends NyPage<CartPage> {
  List<CartLineItem> _cartLines = [];

  @override
  LoadingStyle loadingStyle = LoadingStyle.skeletonizer();

  @override
  bool get stateManaged => true;

  @override
  get init => () async {
        await _cartCheck();
        CheckoutSession.getInstance.coupon = null;
      };

  _cartCheck() async {
    List<CartLineItem> cart = await Cart.getInstance.getCart();
    if (cart.isEmpty) {
      return;
    }

    // Validate cart items against WooCommerce products
    List<CartLineItem> validCartItems = [];
    WooCommerceService wooCommerceService = WooCommerceService();

    for (CartLineItem cartItem in cart) {
      try {
        // Check if product still exists and is in stock
        if (cartItem.productId != null) {
          MyProduct product = await wooCommerceService.getProduct(cartItem.productId!);
          if (product.stockStatus == WooProductStockStatus.instock.name) {
            validCartItems.add(cartItem);
          }
        }
      } catch (e) {
        // Product not found or error, remove from cart
        print('Error checking product ${cartItem.productId}: $e');
      }
    }

    _cartLines = validCartItems;
    if (_cartLines.isNotEmpty) {
      await Cart.getInstance.saveCartToPref(cartLineItems: _cartLines);
    } else {
      await Cart.getInstance.saveCartToPref(cartLineItems: []);
    }
  }

  void _actionProceedToCheckout() async {
    // Use cached cart items if available, otherwise get from storage
    List<CartLineItem> cartLineItems = _cartLines.isNotEmpty ? _cartLines : await Cart.getInstance.getCart();

    if (isLoading()) {
      return;
    }

    if (cartLineItems.isEmpty) {
      showToast(
        title: trans("Cart"),
        description: trans("You need items in your cart to checkout"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.shopping_cart,
      );
      return;
    }

    if (!cartLineItems.every(
        (c) => c.stockStatus == 'instock' || c.stockStatus == 'onbackorder')) {
      showToast(
        title: trans("Cart"),
        description: trans("There is an item out of stock"),
        style: ToastNotificationStyleType.warning,
        icon: Icons.shopping_cart,
      );
      return;
    }

    CheckoutSession.getInstance.initSession();
    CustomerAddress? sfCustomerAddress =
        await CheckoutSession.getInstance.getBillingAddress();

    if (sfCustomerAddress != null) {
      CheckoutSession.getInstance.billingDetails!.billingAddress =
          sfCustomerAddress;
      CheckoutSession.getInstance.billingDetails!.shippingAddress =
          sfCustomerAddress;
    }

    if (!(await AuthService().isLoggedIn())) {
      // show modal to ask customer if they would like to checkout as guest or login
      if (!mounted) return;
      showAdaptiveDialog(
          context: context,
          builder: (context) {
            return AlertDialog.adaptive(
              content: Text("Checkout as guest or login to continue".tr())
                  .headingMedium(),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    routeTo(CheckoutConfirmationPage.path);
                  },
                  child: Text("Checkout as guest".tr()),
                ),
                if (AppHelper.instance.appConfig!.wpLoginEnabled == 1)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      UserAuth.instance.redirect =
                          CheckoutConfirmationPage.path.name;
                      routeTo(AccountLoginPage.path);
                    },
                    child: Text("Login / Create an account".tr()),
                  ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text("Cancel".tr()),
                )
              ],
            );
          });
      return;
    }
    routeTo(CheckoutConfirmationPage.path);
  }

  _showToastCartCleared() {
    showToast(
        title: trans("Success"),
        description: trans("Cart cleared"),
        style: ToastNotificationStyleType.success,
        icon: Icons.delete_outline);
  }

  _showToastMaximumStockReached() {
    showToast(
      title: trans("Cart"),
      description: trans("Maximum stock reached"),
      style: ToastNotificationStyleType.warning,
      icon: Icons.shopping_cart,
    );
  }

  _showToastCartItemRemoved() {
    showToast(
      title: trans("Updated"),
      description: trans("Item removed"),
      style: ToastNotificationStyleType.warning,
      icon: Icons.remove_shopping_cart,
    );
  }

  @override
  stateUpdated(data) async {
    if (data["action"] == "showToastMaximumStockReached") {
      _showToastMaximumStockReached();
      return;
    }
    if (data["action"] == "showToastCartItemRemoved") {
      _showToastCartItemRemoved();
      return;
    }
    if (data["action"] == "showToastCartCleared") {
      _showToastCartCleared();
      return;
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(
          'سلة التسوق',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: true, // Enable back arrow
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).appBarTheme.iconTheme?.color ?? ThemeColor.get(context).textPrimary,
          ),
          onPressed: () {
            // Navigation safety check to prevent black screen
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // Fallback to home if no previous route
              routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
            }
          },
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.delete_outline,
              color: ThemeColor.get(context).brandPrimary,
              size: 28,
            ),
            onPressed: () {
              _showClearCartDialog(context);
            },
          ),
        ],
      ),
      body: SafeAreaWidget(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: NyListView(
                child: (context, cartLineItem) {
                  return CartProductItem(cartLineItem);
                },
                data: () async {
                  if (hasInitComplete) {
                    return await Cart.getInstance.getCart();
                  }
                  return _cartLines;
                },
                stateName: "shopping_cart_items_list_view",
                loadingStyle: LoadingStyle.none(),
                empty: SharedEmptyStateWidget(
                  iconData: Icons.shopping_cart_outlined,
                  title: 'السلة فارغة',
                  description: 'لم تقم بإضافة أي منتجات بعد\nابدأ التسوق واكتشف منتجاتنا المميزة',
                  buttonText: 'ابدأ التسوق',
                  onButtonPressed: () {
                    routeTo(HomePage.path);
                  },
                ),
              ),
            ),
            // Coupon Section
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  top: BorderSide(color: ThemeColor.get(context).borderSecondary, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: InputDecoration(
                        hintText: 'أدخل كود الخصم',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        prefixIcon: Icon(Icons.local_offer_outlined, color: ThemeColor.get(context).brandPrimary),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement coupon application logic
                      showToast(
                        title: "كود الخصم",
                        description: "سيتم تطبيق هذه الميزة قريباً",
                        style: ToastNotificationStyleType.info,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeColor.get(context).brandPrimary,
                      foregroundColor: ThemeColor.get(context).textOnPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: Text(
                      'تطبيق',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
            // Total and Checkout Section
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                boxShadow: [
                  BoxShadow(
                    color: ThemeColor.get(context).shadowLight,
                    blurRadius: 8,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الإجمالي',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: ThemeColor.get(context).textPrimary,
                            ),
                      ),
                      ShoppingCartTotal(),
                    ],
                  ),
                  SizedBox(height: 20),
                  // Enhanced checkout button with proper constraints
                  SizedBox(
                    width: double.infinity,
                    height: 60, // Increased height for better visibility
                    child: ElevatedButton(
                      onPressed: _actionProceedToCheckout,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColor.get(context).brandPrimary,
                        foregroundColor: ThemeColor.get(context).textOnPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 4,
                        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 18),
                        minimumSize: Size(double.infinity, 60),
                        maximumSize: Size(double.infinity, 60),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.lock_outline, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'متابعة إلى الدفع',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              height: 1.2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  void _showClearCartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'مسح السلة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: ThemeColor.get(context).textPrimary,
            ),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد مسح جميع المنتجات من السلة؟',
            style: TextStyle(
              color: ThemeColor.get(context).textSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: ThemeColor.get(context).textMuted,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                event<CartRemoveAllEvent>();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColor.get(context).brandPrimary,
                foregroundColor: ThemeColor.get(context).textOnPrimary,
              ),
              child: Text('مسح'),
            ),
          ],
        );
      },
    );
  }
}
