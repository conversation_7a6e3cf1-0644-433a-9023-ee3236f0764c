{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4971,5014,5057,5112,5170,5228,5319,5411,5488,5548,5611,5670,5745,5808,5868,5930,6001,6059,6133,6202,6279,6328,6401,6446,6496,6552,6608,6669,6728,6789,6860,6919,6964", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4966,5009,5052,5107,5165,5223,5314,5406,5483,5543,5606,5665,5740,5803,5863,5925,5996,6054,6128,6197,6274,6323,6396,6441,6491,6547,6603,6664,6723,6784,6855,6914,6959,7021"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,362,363,365,366,370,371,373,378,385,386,457,458,459,461,466,479,480,481,482,483,484,485,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16886,16946,17014,17075,17136,17205,17259,17505,17553,17618,17684,17883,18038,18122,18259,18584,18845,19788,20001,20080,20143,20279,20352,20422,20562,20824,20900,20970,21053,21118,21193,21266,21335,21404,21482,21615,21818,21910,22477,22540,22601,23631,23948,24013,24097,24177,24232,24308,24380,24438,24509,24582,24637,25093,25138,25208,25435,25492,25557,25624,25949,26256,26320,26397,26461,26504,26768,26823,26881,26939,27030,27122,27365,27425,28059,28118,28260,28323,28589,28651,28782,29206,29632,29701,35574,35623,35696,35821,36215,37472,37528,37589,37648,37709,37780,37839,38932", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "16941,17009,17070,17131,17200,17254,17314,17548,17613,17679,17747,17962,18117,18190,18323,18649,18911,19863,20075,20138,20214,20347,20417,20500,20627,20895,20965,21048,21113,21188,21261,21330,21399,21477,21537,21677,21905,21990,22535,22596,22923,23943,24008,24092,24172,24227,24303,24375,24433,24504,24577,24632,24702,25133,25203,25285,25487,25552,25619,25686,25988,26315,26392,26456,26499,26542,26818,26876,26934,27025,27117,27194,27420,27483,28113,28188,28318,28378,28646,28717,28835,29275,29696,29773,35618,35691,35736,35866,36266,37523,37584,37643,37704,37775,37834,37879,38989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3073,3136,3197,3264,3333,4071,4161,4268,9257,9308,9522,10950,11160,11218,11296,11357,11414,11470,11529,11587,11641,11727,11783,11841,11895,11960,12053,12127,12199,12279,12353,12431,12551,12614,12677,12776,12853,12927,12977,13028,13094,13158,13226,13297,13369,13430,13501,13568,13628,13716,13796,13859,13942,14027,14101,14166,14242,14290,14364,14428,14504,14582,14644,14708,14771,14837,14917,14997,15073,15154,15208,15611,16026,16101,16241", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,3131,3192,3259,3328,3405,4156,4263,4336,9303,9365,9595,11004,11213,11291,11352,11409,11465,11524,11582,11636,11722,11778,11836,11890,11955,12048,12122,12194,12274,12348,12426,12546,12609,12672,12771,12848,12922,12972,13023,13089,13153,13221,13292,13364,13425,13496,13563,13623,13711,13791,13854,13937,14022,14096,14161,14237,14285,14359,14423,14499,14577,14639,14703,14766,14832,14912,14992,15068,15149,15203,15258,15675,16096,16169,16306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1192,1272,1416,1476,1536,1587,1638,1704,1788,1865,1920,1991,2058,2125,2185,2260,2413,2479,2551,2605,2663,2721,2779,2840,2906,2996,3073,3150,3240,3324,3394,3473,3558,3659,3768,3860,3954,4003,4239,4314,4371", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1187,1267,1411,1471,1531,1582,1633,1699,1783,1860,1915,1986,2053,2120,2180,2255,2408,2474,2546,2600,2658,2716,2774,2835,2901,2991,3068,3145,3235,3319,3389,3468,3553,3654,3763,3855,3949,3998,4234,4309,4366,4421"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,354,355,356,357,358,364,367,372,374,375,376,377,380,382,383,387,391,392,394,396,408,433,434,435,436,437,455,462,463,464,465,467,468,469,486", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21995,22084,22219,22365,22928,22972,23030,23105,23169,23287,23342,24707,24898,25029,25290,25355,25691,26136,26196,26655,27314,27488,27554,27638,27715,27770,28193,28383,28722,28840,28915,29068,29134,29331,29457,29515,29778,30171,30232,30383,30555,31806,33693,33783,33867,33937,34016,35410,35871,35980,36072,36166,36271,36507,36582,37884", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "22079,22214,22291,22472,22967,23025,23100,23164,23282,23337,23409,24758,24946,25088,25350,25430,25830,26191,26251,26701,27360,27549,27633,27710,27765,27836,28255,28445,28777,28910,29063,29129,29201,29380,29510,29568,29831,30227,30293,30468,30627,31878,33778,33862,33932,34011,34096,35506,35975,36067,36161,36210,36502,36577,36634,37934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,15947", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,16021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,289,400,509,589,672,748,833,918,1026,1128,1214,1300,1391,1500,1578,1657,1780,1873,1969,2075,2169", "endColumns": "130,102,110,108,79,82,75,84,84,107,101,85,85,90,108,77,78,122,92,95,105,93,99", "endOffsets": "181,284,395,504,584,667,743,828,913,1021,1123,1209,1295,1386,1495,1573,1652,1775,1868,1964,2070,2164,2264"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4694,4797,4908,5017,5097,5180,5256,5341,5426,5534,5636,5722,5808,5899,6008,6086,6165,6288,6381,6477,6583,6677", "endColumns": "130,102,110,108,79,82,75,84,84,107,101,85,85,90,108,77,78,122,92,95,105,93,99", "endOffsets": "4689,4792,4903,5012,5092,5175,5251,5336,5421,5529,5631,5717,5803,5894,6003,6081,6160,6283,6376,6472,6578,6672,6772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "95,104,185,189,518,524,525", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8701,9370,15415,15680,40153,40735,40814", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "8762,9446,15480,15794,40316,40809,40885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16412,16487,16571,16636,16702,16762,16824", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "16482,16566,16631,16697,16757,16819,16881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,108", "endOffsets": "156,265"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2858,2964", "endColumns": "105,108", "endOffsets": "2959,3068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "8767,9600,9692,9793", "endColumns": "82,91,100,92", "endOffsets": "8845,9687,9788,9881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6777,6878,7006,7121,7223,7330,7446,7548,7749,7859,7960,8089,8204,8311,8419,8474,8531", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "6873,7001,7116,7218,7325,7441,7543,7636,7854,7955,8084,8199,8306,8414,8469,8526,8598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,359,360,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17752,17967,18195,18328,18401,18466,18916,18979,19102,19162,19230,19294,19355,19413,19479,19598,19663,19868,20632,20691,21542,21682,21747,24763,24835,25835,26547,26593,26706,27199,27252,27841,27913,28450,28520", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "17807,18033,18254,18396,18461,18522,18974,19035,19157,19225,19289,19350,19408,19474,19536,19658,19716,19930,20686,20756,21610,21742,21813,24830,24893,25875,26588,26650,26763,27247,27309,27908,27975,28515,28584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "7641", "endColumns": "107", "endOffsets": "7744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,738,840,930,1038,1141", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "148,229,328,431,533,631,733,835,925,1033,1136,1232"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8603,8938,9886,9985,10088,10190,10288,10390,10492,10582,10690,10793", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "8696,9014,9980,10083,10185,10283,10385,10487,10577,10685,10788,10884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17812,18527,18654,18714,18787,19040,19541,19721,19935,20219,20505,20761", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "17878,18579,18709,18782,18840,19097,19593,19783,19996,20274,20557,20819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "522,523", "startColumns": "4,4", "startOffsets": "40577,40658", "endColumns": "80,76", "endOffsets": "40653,40730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3410,3502,3601,3695,3789,3882,3975,16311", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3497,3596,3690,3784,3877,3970,4066,16407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4341,4417,8850,9019,9179,11009,11083,15263,15341,15485,15548,15799,15872,16174,40321,40396,40461", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4412,4486,8933,9105,9252,11078,11155,15336,15410,15543,15606,15867,15942,16236,40391,40456,40572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3216,3294,3371,3437,3515,3592,3677,3798,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5486,5544,5621,5705,5806,5873,5936,6016,6066,6117,6175,6244,6362,6527,6710,6789,6849,6914,6973,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3211,3289,3366,3432,3510,3587,3672,3793,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5481,5539,5616,5700,5801,5868,5931,6011,6061,6112,6170,6239,6357,6522,6705,6784,6844,6909,6968,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,361,379,381,384,388,389,390,393,395,397,398,399,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,460,470,471,472,473,474,475,476,477,478,487,488,489,490,491,492,493,494,495,496,497,498,499,500,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17319,17378,17444,22296,23414,23485,23558,24951,25880,25993,26064,27980,29280,29385,29573,29836,29954,30076,30298,30473,30632,30716,30802,30906,30994,31080,31199,31284,31383,31551,31718,31883,31973,32075,32154,32211,32270,32345,32423,32500,32566,32644,32721,32806,32927,32990,33052,33113,33203,33273,33346,33405,33474,33542,33605,34101,34182,34273,34354,34419,34490,34554,34625,34681,34764,34840,34924,35023,35081,35158,35242,35343,35511,35741,36639,36689,36740,36798,36867,36985,37150,37333,37412,37939,38004,38063,38144,38229,38302,38370,38435,38504,38567,38642,38720,38804,38869,38994,39044,39157,39222,39275,39343,39405,39483,39549,39634,39720,39798,39869,39936,39999,40059", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "17373,17439,17500,22360,23480,23553,23626,25024,25944,26059,26131,28054,29326,29452,29627,29949,30071,30166,30378,30550,30711,30797,30901,30989,31075,31194,31279,31378,31546,31713,31801,31968,32070,32149,32206,32265,32340,32418,32495,32561,32639,32716,32801,32922,32985,33047,33108,33198,33268,33341,33400,33469,33537,33600,33688,34177,34268,34349,34414,34485,34549,34620,34676,34759,34835,34919,35018,35076,35153,35237,35338,35405,35569,35816,36684,36735,36793,36862,36980,37145,37328,37407,37467,37999,38058,38139,38224,38297,38365,38430,38499,38562,38637,38715,38799,38864,38927,39039,39152,39217,39270,39338,39400,39478,39544,39629,39715,39793,39864,39931,39994,40054,40148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4491,9110,9451", "endColumns": "71,68,70", "endOffsets": "4558,9174,9517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "120,526", "startColumns": "4,4", "startOffsets": "10889,40890", "endColumns": "60,71", "endOffsets": "10945,40957"}}]}]}