<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.velvete.ly"
    android:versionCode="1"
    android:versionName="1.0.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />
    <!--
         Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
        <!-- Added to check the default browser that will host the AuthFlow. -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="http" />
        </intent>

        <package android:name="com.facebook.katana" /> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <application
        android:name="com.velvete.ly.MainApplication"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="false"
        android:icon="@mipmap/ic_launcher"
        android:label="Velvete Store"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.velvete.ly.MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize" >
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />

        <!-- Facebook Configuration - TEMPORARILY DISABLED -->
        <!--
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id"/>
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token"/>

        Facebook Activity for Login
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>
        -->
        <service
            android:name="com.baseflow.geolocator.GeolocatorLocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <provider
            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
            android:authorities="com.velvete.ly.flutter.image_provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_image_picker_file_paths" />
        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>

            <meta-data
                android:name="photopicker_activity:0:required"
                android:value="" />
        </service>

        <activity
            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />

        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
            android:exported="false"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
            android:exported="false"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
            android:exported="false"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@style/ThemeTransparent" />

        <receiver
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
            android:enabled="true"
            android:exported="false" />

        <meta-data
            android:name="io.flutter.embedded_views_preview"
            android:value="true" />

        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
                <data
                    android:host="link-accounts"
                    android:pathPrefix="/com.velvete.ly/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
                <data
                    android:host="link-native-accounts"
                    android:pathPrefix="/com.velvete.ly/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
                <data
                    android:host="link-accounts"
                    android:path="/com.velvete.ly/success"
                    android:scheme="stripe-auth" />
                <data
                    android:host="link-accounts"
                    android:path="/com.velvete.ly/cancel"
                    android:scheme="stripe-auth" />

                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
                <data
                    android:host="native-redirect"
                    android:pathPrefix="/com.velvete.ly"
                    android:scheme="stripe-auth" />

                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
                <data
                    android:host="auth-redirect"
                    android:pathPrefix="/com.velvete.ly"
                    android:scheme="stripe" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
        <activity
            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:theme="@style/com_facebook_activity_theme" />
        <activity android:name="com.facebook.CustomTabMainActivity" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="cct.com.velvete.ly"
                    android:scheme="fbconnect" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.link.LinkActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:label="@string/stripe_link"
            android:theme="@style/StripeLinkBaseTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.link.LinkForegroundActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTop"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="complete"
                    android:path="/com.velvete.ly"
                    android:scheme="link-popup" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentRelayActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" />
        <!--
        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
        launched the browser Activity will also handle the return URL deep link.
        -->
        <activity
            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Must match `DefaultReturnUrl#value`. -->
                <data
                    android:host="payment_return_url"
                    android:path="/com.velvete.ly"
                    android:scheme="stripesdk" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" />

        <service
            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
            android:enabled="true"
            android:exported="false" >
            <meta-data
                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
        </service>

        <activity
            android:name="androidx.credentials.playservices.HiddenActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:enabled="true"
            android:exported="false"
            android:fitsSystemWindows="true"
            android:theme="@style/Theme.Hidden" >
        </activity>
        <activity
            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:enabled="true"
            android:exported="false"
            android:fitsSystemWindows="true"
            android:theme="@style/Theme.Hidden" >
        </activity>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <activity
            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
            android:exported="false"
            android:theme="@style/Stripe3DS2Theme" />
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />
        <!--
         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
         with the application context. This config is merged in with the host app's manifest,
         but there can only be one provider with the same authority activated at any given
         point; so if the end user has two or more different apps that use Facebook SDK, only the
         first one will be able to use the provider. To work around this problem, we use the
         following placeholder in the authority to identify each host application as if it was
         a completely different provider.
        -->
        <provider
            android:name="com.facebook.internal.FacebookInitProvider"
            android:authorities="com.velvete.ly.FacebookInitProvider"
            android:exported="false" />

        <receiver
            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.velvete.ly.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.velvete.ly.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <meta-data
            android:name="aia-compat-api-min-version"
            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
        <activity
            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
            android:exported="false"
            android:stateNotNeeded="true"
            android:theme="@style/Theme.PlayCore.Transparent" />
    </application>

</manifest>