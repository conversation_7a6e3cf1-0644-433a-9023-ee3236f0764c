{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,253,376,517,649,776,912,1055,1154,1303,1453", "endColumns": "109,87,122,140,131,126,135,142,98,148,149,125", "endOffsets": "160,248,371,512,644,771,907,1050,1149,1298,1448,1574"}, "to": {"startLines": "93,97,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9529,9916,10936,11059,11200,11332,11459,11595,11738,11837,11986,12136", "endColumns": "109,87,122,140,131,126,135,142,98,148,149,125", "endOffsets": "9634,9999,11054,11195,11327,11454,11590,11733,11832,11981,12131,12257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,101,102,105,120,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,187,192,193,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3208,3286,3362,3440,3537,4344,4444,4593,10261,10320,10547,12323,12562,12622,12709,12773,12835,12897,12965,13030,13084,13202,13260,13321,13377,13452,13578,13664,13741,13832,13916,13996,14137,14215,14295,14417,14503,14581,14637,14688,14754,14822,14896,14967,15042,15114,15192,15262,15335,15439,15523,15600,15688,15777,15851,15924,16009,16058,16136,16202,16282,16365,16427,16491,16554,16623,16731,16834,16935,17034,17094,17529,18002,18082,18230", "endLines": "5,35,36,37,38,39,47,48,49,101,102,105,120,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,187,192,193,195", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3281,3357,3435,3532,3612,4439,4588,4666,10315,10379,10628,12391,12617,12704,12768,12830,12892,12960,13025,13079,13197,13255,13316,13372,13447,13573,13659,13736,13827,13911,13991,14132,14210,14290,14412,14498,14576,14632,14683,14749,14817,14891,14962,15037,15109,15187,15257,15330,15434,15518,15595,15683,15772,15846,15919,16004,16053,16131,16197,16277,16360,16422,16486,16549,16618,16726,16829,16930,17029,17089,17144,17604,18077,18155,18302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "52,99,104", "startColumns": "4,4,4", "startOffsets": "4843,10103,10472", "endColumns": "74,75,74", "endOffsets": "4913,10174,10542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,324,419,628,676,743,842,911,1166,1226,1318,1389,1444,1508,1585,1678,1996,2070,2135,2188,2241,2337,2461,2577,2634,2721,2800,2883,2949,3048,3346,3423,3500,3567,3627,3689,3749,3818,3895,3995,4091,4183,4287,4379,4452,4531,4616,4814,5021,5133,5253,5308,6025,6124,6188", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "162,319,414,623,671,738,837,906,1161,1221,1313,1384,1439,1503,1580,1673,1991,2065,2130,2183,2236,2332,2456,2572,2629,2716,2795,2878,2944,3043,3341,3418,3495,3562,3622,3684,3744,3813,3890,3990,4086,4178,4282,4374,4447,4526,4611,4809,5016,5128,5248,5303,6020,6119,6183,6238"}, "to": {"startLines": "280,281,282,284,288,289,290,291,292,293,294,310,313,315,319,320,325,331,332,340,350,354,355,356,357,358,364,367,372,374,375,376,377,380,382,383,387,391,392,394,396,408,433,434,435,436,437,455,462,463,464,465,467,468,469,486", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24706,24818,24975,25162,26181,26229,26296,26395,26464,26719,26779,28683,28948,29132,29420,29497,29864,30554,30628,31164,32069,32352,32448,32572,32688,32745,33311,33563,33989,34135,34234,34532,34609,34846,35032,35092,35386,35953,36022,36188,36394,38055,40403,40507,40599,40672,40751,42369,43038,43245,43357,43477,43599,44316,44415,46143", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "24813,24970,25065,25366,26224,26291,26390,26459,26714,26774,26866,28749,28998,29191,29492,29585,30177,30623,30688,31212,32117,32443,32567,32683,32740,32827,33385,33641,34050,34229,34527,34604,34681,34908,35087,35149,35441,36017,36094,36283,36485,38142,40502,40594,40667,40746,40831,42562,43240,43352,43472,43527,44311,44410,44474,46193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "197,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18408,18497,18601,18668,18735,18801,18874", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "18492,18596,18663,18730,18796,18869,18936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,211,291,383,470,548,625,754,846,944,1033,1150,1208,1327,1391,1569,1760,1898,1987,2093,2184,2285,2466,2580,2678,2927,3035,3160,3352,3562,3658,3770,3958,4052,4110,4174,4255,4350,4451,4528,4628,4717,4827,5040,5109,5175,5249,5354,5431,5520,5586,5667,5738,5806,5914,6001,6111,6201,6272,6360,6427,6513,6570,6670,6776,6879,7005,7064,7155,7255,7376,7447,7534,7632,7686,7742,7816,7918,8087,8352,8642,8744,8815,8905,8976,9081,9196,9287,9361,9430,9515,9599,9687,9803,9937,10004,10074,10128,10298,10367,10426,10500,10574,10651,10732,10868,10992,11105,11188,11263,11348,11415", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "119,206,286,378,465,543,620,749,841,939,1028,1145,1203,1322,1386,1564,1755,1893,1982,2088,2179,2280,2461,2575,2673,2922,3030,3155,3347,3557,3653,3765,3953,4047,4105,4169,4250,4345,4446,4523,4623,4712,4822,5035,5104,5170,5244,5349,5426,5515,5581,5662,5733,5801,5909,5996,6106,6196,6267,6355,6422,6508,6565,6665,6771,6874,7000,7059,7150,7250,7371,7442,7529,7627,7681,7737,7811,7913,8082,8347,8637,8739,8810,8900,8971,9076,9191,9282,9356,9425,9510,9594,9682,9798,9932,9999,10069,10123,10293,10362,10421,10495,10569,10646,10727,10863,10987,11100,11183,11258,11343,11410,11567"}, "to": {"startLines": "211,212,213,283,295,296,297,314,327,329,330,361,379,381,384,388,389,390,393,395,397,398,399,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,460,470,471,472,473,474,475,476,477,478,487,488,489,490,491,492,493,494,495,496,497,498,499,500,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19408,19477,19564,25070,26871,26958,27036,29003,30230,30367,30465,33016,34788,34913,35154,35446,35624,35815,36099,36288,36490,36581,36682,36863,36977,37075,37324,37432,37557,37749,37959,38147,38259,38447,38541,38599,38663,38744,38839,38940,39017,39117,39206,39316,39529,39598,39664,39738,39843,39920,40009,40075,40156,40227,40295,40836,40923,41033,41123,41194,41282,41349,41435,41492,41592,41698,41801,41927,41986,42077,42177,42298,42567,42886,44479,44533,44589,44663,44765,44934,45199,45489,45591,46198,46288,46359,46464,46579,46670,46744,46813,46898,46982,47070,47186,47320,47387,47533,47587,47757,47826,47885,47959,48033,48110,48191,48327,48451,48564,48647,48722,48807,48874", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "19472,19559,19639,25157,26953,27031,27108,29127,30317,30460,30549,33128,34841,35027,35213,35619,35810,35948,36183,36389,36576,36677,36858,36972,37070,37319,37427,37552,37744,37954,38050,38254,38442,38536,38594,38658,38739,38834,38935,39012,39112,39201,39311,39524,39593,39659,39733,39838,39915,40004,40070,40151,40222,40290,40398,40918,41028,41118,41189,41277,41344,41430,41487,41587,41693,41796,41922,41981,42072,42172,42293,42364,42649,42979,44528,44584,44658,44760,44929,45194,45484,45586,45657,46283,46354,46459,46574,46665,46739,46808,46893,46977,47065,47181,47315,47382,47452,47582,47752,47821,47880,47954,48028,48105,48186,48322,48446,48559,48642,48717,48802,48869,49026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "219,229,231,232,233,237,245,248,251,255,259,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19992,20792,20926,20988,21073,21352,21877,22069,22308,22621,22961,23254", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "20065,20844,20983,21068,21128,21417,21931,22137,22375,22682,23016,23316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "119,526", "startColumns": "4,4", "startOffsets": "12262,49799", "endColumns": "60,73", "endOffsets": "12318,49868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2974,3089", "endColumns": "114,118", "endOffsets": "3084,3203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3617,3713,3815,3912,4010,4117,4226,18307", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3708,3810,3907,4005,4112,4221,4339,18403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3157,3227,3292,3967,4618,4692,4809,4909,4964,5062,5152,5220,5311,5397,5454,5537,5588,5665,5761,5831,5902,5969,6035,6080,6158,6250,6326,6373,6421,6489,6547,6612,6794,6957,7073,7137,7221,7298,7399,7488,7572,7648,7740,7820,7922,8004,8090,8143,8274,8322,8376,8443,8510,8584,8648,8720,8808,8874,8924", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3152,3222,3287,3962,4613,4687,4804,4904,4959,5057,5147,5215,5306,5392,5449,5532,5583,5660,5756,5826,5897,5964,6030,6075,6153,6245,6321,6368,6416,6484,6542,6607,6789,6952,7068,7132,7216,7293,7394,7483,7567,7643,7735,7815,7917,7999,8085,8138,8269,8317,8371,8438,8505,8579,8643,8715,8803,8869,8919,8995"}, "to": {"startLines": "204,205,206,207,208,209,210,214,215,216,217,220,222,223,225,230,234,249,252,253,254,256,257,258,260,264,265,266,267,268,269,270,271,272,273,275,278,279,285,286,287,298,299,300,301,302,303,304,305,306,307,308,309,316,317,318,321,322,323,324,328,333,334,335,336,337,342,343,344,345,346,347,351,352,362,363,365,366,370,371,373,378,385,386,457,458,459,461,466,479,480,481,482,483,484,485,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18941,19009,19086,19146,19214,19289,19349,19644,19697,19769,19849,20070,20246,20344,20497,20849,21133,22142,22380,22469,22535,22687,22773,22857,23021,23321,23408,23490,23592,23666,23754,23850,23936,24017,24116,24262,24505,24609,25371,25441,25506,27113,27764,27838,27955,28055,28110,28208,28298,28366,28457,28543,28600,29196,29247,29324,29590,29660,29731,29798,30322,30693,30771,30863,30939,30986,31279,31347,31405,31470,31652,31815,32122,32186,33133,33210,33390,33479,33821,33897,34055,34686,35218,35300,42654,42707,42838,42984,43532,45662,45729,45803,45867,45939,46027,46093,47457", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "19004,19081,19141,19209,19284,19344,19403,19692,19764,19844,19922,20163,20339,20425,20571,20921,21211,22230,22464,22530,22616,22768,22852,22956,23096,23403,23485,23587,23661,23749,23845,23931,24012,24111,24183,24332,24604,24701,25436,25501,26176,27759,27833,27950,28050,28105,28203,28293,28361,28452,28538,28595,28678,29242,29319,29415,29655,29726,29793,29859,30362,30766,30858,30934,30981,31029,31342,31400,31465,31647,31810,31926,32181,32265,33205,33306,33474,33558,33892,33984,34130,34783,35295,35381,42702,42833,42881,43033,43594,45724,45798,45862,45934,46022,46088,46138,47528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7327,7433,7593,7720,7829,7972,8097,8217,8449,8605,8711,8873,9000,9145,9323,9389,9451", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "7428,7588,7715,7824,7967,8092,8212,8317,8600,8706,8868,8995,9140,9318,9384,9446,9524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "8322", "endColumns": "126", "endOffsets": "8444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "94,103,184,188,518,524,525", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9639,10384,17320,17609,49031,49642,49722", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "9704,10467,17394,17752,49195,49717,49794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,17922", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,17997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,331,464,577,667,761,838,929,1018,1130,1237,1327,1417,1517,1633,1713,1909,2006,2106,2218,2321", "endColumns": "148,126,132,112,89,93,76,90,88,111,106,89,89,99,115,79,195,96,99,111,102,142", "endOffsets": "199,326,459,572,662,756,833,924,1013,1125,1232,1322,1412,1512,1628,1708,1904,2001,2101,2213,2316,2459"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4918,5067,5194,5327,5440,5530,5624,5701,5792,5881,5993,6100,6190,6280,6380,6496,6576,6772,6869,6969,7081,7184", "endColumns": "148,126,132,112,89,93,76,90,88,111,106,89,89,99,115,79,195,96,99,111,102,142", "endOffsets": "5062,5189,5322,5435,5525,5619,5696,5787,5876,5988,6095,6185,6275,6375,6491,6571,6767,6864,6964,7076,7179,7322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "353", "startColumns": "4", "startOffsets": "32270", "endColumns": "81", "endOffsets": "32347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "95,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "9709,10633,10732,10839", "endColumns": "111,98,106,96", "endOffsets": "9816,10727,10834,10931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "218,221,224,226,227,228,235,236,238,239,240,241,242,243,244,246,247,250,261,262,274,276,277,311,312,326,338,339,341,348,349,359,360,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19927,20168,20430,20576,20664,20731,21216,21285,21422,21486,21554,21622,21682,21740,21813,21936,22006,22235,23101,23165,24188,24337,24414,28754,28863,30182,31034,31089,31217,31931,32000,32832,32929,33646,33734", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "19987,20241,20492,20659,20726,20787,21280,21347,21481,21549,21617,21677,21735,21808,21872,22001,22064,22303,23160,23249,24257,24409,24500,28858,28943,30225,31084,31159,31274,31995,32064,32924,33011,33729,33816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "50,51,96,98,100,121,122,182,183,185,186,189,190,194,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4671,4763,9821,10004,10179,12396,12473,17149,17238,17399,17464,17757,17838,18160,49200,49278,49345", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4758,4838,9911,10098,10256,12468,12557,17233,17315,17459,17524,17833,17917,18225,49273,49340,49460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "522,523", "startColumns": "4,4", "startOffsets": "49465,49555", "endColumns": "89,86", "endOffsets": "49550,49637"}}]}]}