{"assets": [{"h": 256, "id": "0", "p": "data:image/png;base64,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*****************************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", "u": "", "w": 256, "e": 1}, {"id": "25", "layers": [{"ind": 24, "ty": 4, "parent": 23, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [212.16, 48]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [424.32, 96]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 23, "ty": 3, "parent": 22, "ks": {"p": {"a": 0, "k": [-96, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 22, "ty": 3, "ks": {"p": {"a": 0, "k": [96, 0]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "29", "layers": [{"ind": 27, "ty": 0, "parent": 21, "ks": {"a": {"a": 0, "k": [96, 0]}}, "w": 521, "h": 96, "ip": 0, "op": 121, "st": 0, "refId": "25"}, {"ind": 28, "ty": 4, "parent": 21, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 61.38, "s": [0], "h": 1}, {"t": 61.38, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}]}}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [115.32, 38.98]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [230.64, 231.96]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 21, "ty": 3, "ks": {"p": {"a": 0, "k": [96, 77]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "18", "layers": [{"ind": 9, "ty": 4, "parent": 8, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [4, 2], [2.2, 3.4], [0, 4.3], [0, 0], [-2.2, 3.4], [-3.8, 1.9], [-4.7, 0], [0, 0], [-3.7, -1.7], [-2.2, -3.4], [0, -5], [0, 0], [0.1, -0.8], [0.1, -0.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, 1.2], [1.1, 0.7], [1.5, 0], [0, 0], [1.1, -0.7], [0.6, -1.2], [0, -1.7], [0, 0], [0, 0], [-0.7, -1.3], [-1.4, -0.7], [-2, 0], [0, 0], [-1.3, 0.5], [-1.4, 1.1], [0, 0], [0, 0], [3.1, -1.2], [4.4, 0]], "v": [[28.2, 77.5], [28.2, 77.5], [14.2, 74.5], [5, 66.4], [1.7, 54.8], [1.7, 54.8], [4.9, 43], [13.8, 35], [26.5, 32.1], [26.5, 32.1], [38.4, 34.7], [47.2, 42.3], [50.6, 54.8], [50.6, 54.8], [50.5, 56.9], [50.3, 59], [50.3, 59], [16.6, 59], [16.6, 50.3], [40.7, 50.3], [33.9, 52.6], [33, 48.2], [30.5, 45.4], [26.6, 44.4], [26.6, 44.4], [22.8, 45.4], [20.2, 48.2], [19.4, 52.6], [19.4, 52.6], [19.4, 55.3], [20.5, 60.1], [23.7, 63.1], [28.7, 64.1], [28.7, 64.1], [33.7, 63.3], [37.8, 60.9], [37.8, 60.9], [47.3, 70.4], [39.4, 75.7], [28.2, 77.5]], "o": [[0, 0], [-5.3, 0], [-4, -2], [-2.2, -3.4], [0, 0], [0, -4.4], [2.2, -3.4], [3.8, -1.9], [0, 0], [4.3, 0], [3.7, 1.7], [2.2, 3.4], [0, 0], [0, 0.6], [-0.1, 0.8], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.7], [-0.6, -1.2], [-1.1, -0.7], [0, 0], [-1.5, 0], [-1.1, 0.7], [-0.6, 1.2], [0, 0], [0, 0], [0, 1.9], [0.7, 1.3], [1.4, 0.7], [0, 0], [2, 0], [1.3, -0.5], [0, 0], [0, 0], [-2.1, 2.3], [-3.1, 1.2], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 8, "ty": 3, "parent": 7, "ks": {"p": {"a": 0, "k": [180.08, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 11, "ty": 4, "parent": 10, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.9, 1.7], [-4.1, 0], [0, 0], [0, 0], [0.6, 0.1], [0.6, 0], [0, 0], [1.7, -1.4], [0, -3.5], [0, 0], [0, 0]], "v": [[22.1, 76.7], [4, 76.7], [4, 32.9], [21.2, 32.9], [21.2, 46.1], [18.4, 42.4], [24.8, 34.7], [35.4, 32.1], [35.4, 32.1], [35.4, 48.1], [33.2, 47.9], [31.4, 47.8], [31.4, 47.8], [24.7, 49.9], [22.1, 57.2], [22.1, 57.2], [22.1, 76.7]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.3, -3.4], [2.9, -1.7], [0, 0], [0, 0], [-0.8, -0.1], [-0.6, -0.1], [0, 0], [-2.7, 0], [-1.7, 1.4], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 10, "ty": 3, "parent": 7, "ks": {"p": {"a": 0, "k": [143.6, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 13, "ty": 4, "parent": 12, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [3.8, 1.9], [2.2, 3.4], [0, 4.4], [0, 0], [-2.2, 3.4], [-3.8, 1.9], [-5, 0], [0, 0], [-3.8, -1.9], [-2.2, -3.4], [0, -4.4], [0, 0], [2.2, -3.4], [3.8, -1.9], [5, 0]], "v": [[27.1, 77.5], [27.1, 77.5], [14, 74.6], [5, 66.5], [1.7, 54.8], [1.7, 54.8], [5, 43], [14, 35], [27.1, 32.1], [27.1, 32.1], [40.3, 35], [49.3, 43], [52.6, 54.8], [52.6, 54.8], [49.3, 66.5], [40.3, 74.6], [27.1, 77.5]], "o": [[0, 0], [-5, 0], [-3.8, -1.9], [-2.2, -3.4], [0, 0], [0, -4.4], [2.2, -3.4], [3.8, -1.9], [0, 0], [5, 0], [3.8, 1.9], [2.2, 3.4], [0, 0], [0, 4.4], [-2.2, 3.4], [-3.8, 1.9], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, 0.6], [-0.6, 1.3], [0, 1.9], [0, 0], [0.6, 1.3], [1.1, 0.6], [1.3, 0], [0, 0], [1.1, -0.6], [0.6, -1.3], [0, -1.9], [0, 0], [-0.6, -1.3], [-1.1, -0.6], [-1.3, 0]], "v": [[27.1, 63.4], [27.1, 63.4], [30.7, 62.4], [33.3, 59.5], [34.2, 54.8], [34.2, 54.8], [33.3, 50], [30.7, 47.2], [27.1, 46.2], [27.1, 46.2], [23.5, 47.2], [21, 50], [20, 54.8], [20, 54.8], [21, 59.5], [23.5, 62.4], [27.1, 63.4]], "o": [[0, 0], [1.3, 0], [1.1, -0.6], [0.6, -1.3], [0, 0], [0, -1.9], [-0.6, -1.3], [-1.1, -0.6], [0, 0], [-1.3, 0], [-1.1, 0.6], [-0.6, 1.3], [0, 0], [0, 1.9], [0.6, 1.3], [1.1, 0.6], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 12, "ty": 3, "parent": 7, "ks": {"p": {"a": 0, "k": [89.36, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 15, "ty": 4, "parent": 14, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [3.4, 2.9], [0, 6], [0, 0], [0, 0], [0, 0], [0, 0], [-0.7, -0.7], [-1.1, 0], [0, 0], [-1.2, 0.8], [0, 0], [0, 0], [1.9, -0.4], [2.1, 0]], "v": [[25.1, 77.5], [25.1, 77.5], [11, 73.1], [6, 59.7], [6, 59.7], [6, 23.1], [24.1, 23.1], [24.1, 59.6], [25.2, 62.7], [27.8, 63.8], [27.8, 63.8], [31.9, 62.6], [31.9, 62.6], [36.2, 75.2], [31.2, 76.9], [25.1, 77.5]], "o": [[0, 0], [-6, 0], [-3.4, -2.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.3], [0.7, 0.7], [0, 0], [1.5, 0], [0, 0], [0, 0], [-1.4, 0.8], [-1.9, 0.4], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33, 46.1], [0, 46.1], [0, 32.9], [33, 32.9], [33, 46.1]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 14, "ty": 3, "parent": 7, "ks": {"p": {"a": 0, "k": [52.56, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 17, "ty": 4, "parent": 16, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [4.4, 1.1], [2.8, 1.7], [0, 0], [0, 0], [-3.3, -0.9], [-3.1, 0], [0, 0], [-1, 0.2], [-0.5, 0.5], [0, 0.6], [0, 0], [1.1, 0.6], [1.8, 0.4], [2.2, 0.5], [2.2, 0.7], [1.8, 1.2], [1.1, 2], [0, 3], [0, 0], [-1.9, 2.9], [-3.9, 1.7], [-5.8, 0], [0, 0], [-3.7, -0.8], [-2.9, -1.7], [0, 0], [0, 0], [2.6, 0.7], [2.5, 0], [0, 0], [1.1, -0.3], [0.5, -0.5], [0, -0.6], [0, 0], [-1.1, -0.6], [-1.8, -0.4], [-2.2, -0.4], [-2.2, -0.7], [-1.8, -1.2], [-1.1, -2], [0, -2.9], [0, 0], [1.9, -2.9], [3.8, -1.7], [5.8, 0]], "v": [[25.4, 78], [25.4, 78], [11.8, 76.4], [1, 72.2], [1, 72.2], [7.1, 58.4], [16, 62.2], [25.6, 63.6], [25.6, 63.6], [29.9, 63.2], [32.2, 62.2], [32.9, 60.5], [32.9, 60.5], [31.2, 58.1], [26.8, 56.6], [20.7, 55.4], [14.1, 53.6], [8, 50.6], [3.6, 45.8], [1.9, 38.3], [1.9, 38.3], [4.8, 28.8], [13.6, 22], [28, 19.4], [28, 19.4], [39.2, 20.6], [49.1, 24.3], [49.1, 24.3], [43.4, 38], [35.4, 34.8], [27.8, 33.8], [27.8, 33.8], [23.5, 34.3], [21.2, 35.6], [20.6, 37.3], [20.6, 37.3], [22.2, 39.6], [26.7, 41], [32.8, 42.2], [39.4, 44], [45.4, 46.9], [49.8, 51.7], [51.5, 59.1], [51.5, 59.1], [48.6, 68.5], [39.9, 75.4], [25.4, 78]], "o": [[0, 0], [-4.7, 0], [-4.4, -1.1], [0, 0], [0, 0], [2.7, 1.5], [3.3, 0.9], [0, 0], [1.8, 0], [1, -0.2], [0.5, -0.5], [0, 0], [0, -1], [-1.1, -0.6], [-1.8, -0.4], [-2.2, -0.5], [-2.2, -0.7], [-1.8, -1.2], [-1.1, -2], [0, 0], [0, -3.5], [1.9, -2.9], [3.9, -1.7], [0, 0], [3.8, 0], [3.7, 0.8], [0, 0], [0, 0], [-2.8, -1.4], [-2.6, -0.7], [0, 0], [-1.8, 0], [-1.1, 0.3], [-0.5, 0.5], [0, 0], [0, 1], [1.1, 0.6], [1.8, 0.4], [2.2, 0.4], [2.2, 0.7], [1.8, 1.2], [1.1, 2], [0, 0], [0, 3.4], [-1.9, 2.9], [-3.8, 1.7], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 16, "ty": 3, "parent": 7, "ks": {}, "ip": 0, "op": 121, "st": 0}, {"ind": 7, "ty": 3, "parent": 6, "ks": {"p": {"a": 1, "k": [{"t": 0, "s": [0, -96], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 43.38, "s": [0, -96], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 61.38, "s": [0, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 120, "s": [0, 0], "h": 1}]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 6, "ty": 3, "ks": {"p": {"a": 0, "k": [0, 77]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "32", "layers": [{"ind": 31, "ty": 0, "td": 1, "parent": 5, "ks": {"a": {"a": 0, "k": [96, 77]}}, "w": 617, "h": 232, "ip": 0, "op": 121, "st": 0, "refId": "29"}, {"ind": 20, "ty": 0, "tt": 1, "parent": 5, "ks": {"a": {"a": 0, "k": [0, 77]}}, "w": 231, "h": 155, "ip": 0, "op": 121, "st": 0, "refId": "18"}, {"ind": 5, "ty": 3, "ks": {"p": {"a": 0, "k": [0, 77]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "59", "layers": [{"ind": 58, "ty": 4, "parent": 57, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [263, 48]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [526, 96]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 57, "ty": 3, "parent": 56, "ks": {"p": {"a": 0, "k": [-96, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 56, "ty": 3, "ks": {"p": {"a": 0, "k": [96, 0]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "63", "layers": [{"ind": 61, "ty": 0, "parent": 55, "ks": {"a": {"a": 0, "k": [96, 0]}}, "w": 622, "h": 96, "ip": 0, "op": 121, "st": 0, "refId": "59"}, {"ind": 62, "ty": 4, "parent": 55, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 55.38, "s": [0], "h": 1}, {"t": 55.38, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}]}}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [166.16, 38.74]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [336.32, 235.48]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 55, "ty": 3, "ks": {"p": {"a": 0, "k": [96, 79]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "52", "layers": [{"ind": 39, "ty": 4, "parent": 38, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [4, 2], [2.2, 3.4], [0, 4.3], [0, 0], [-2.2, 3.4], [-3.8, 1.9], [-4.7, 0], [0, 0], [-3.7, -1.7], [-2.2, -3.4], [0, -5], [0, 0], [0.1, -0.8], [0.1, -0.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, 1.2], [1.1, 0.7], [1.5, 0], [0, 0], [1.1, -0.7], [0.6, -1.2], [0, -1.7], [0, 0], [0, 0], [-0.7, -1.3], [-1.4, -0.7], [-2, 0], [0, 0], [-1.3, 0.5], [-1.4, 1.1], [0, 0], [0, 0], [3.1, -1.2], [4.4, 0]], "v": [[28.2, 77.5], [28.2, 77.5], [14.2, 74.5], [5, 66.4], [1.7, 54.8], [1.7, 54.8], [4.9, 43], [13.8, 35], [26.5, 32.1], [26.5, 32.1], [38.4, 34.7], [47.2, 42.3], [50.6, 54.8], [50.6, 54.8], [50.5, 56.9], [50.3, 59], [50.3, 59], [16.6, 59], [16.6, 50.3], [40.7, 50.3], [33.9, 52.6], [33, 48.2], [30.5, 45.4], [26.6, 44.4], [26.6, 44.4], [22.8, 45.4], [20.2, 48.2], [19.4, 52.6], [19.4, 52.6], [19.4, 55.3], [20.5, 60.1], [23.7, 63.1], [28.7, 64.1], [28.7, 64.1], [33.7, 63.3], [37.8, 60.9], [37.8, 60.9], [47.3, 70.4], [39.4, 75.7], [28.2, 77.5]], "o": [[0, 0], [-5.3, 0], [-4, -2], [-2.2, -3.4], [0, 0], [0, -4.4], [2.2, -3.4], [3.8, -1.9], [0, 0], [4.3, 0], [3.7, 1.7], [2.2, 3.4], [0, 0], [0, 0.6], [-0.1, 0.8], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.7], [-0.6, -1.2], [-1.1, -0.7], [0, 0], [-1.5, 0], [-1.1, 0.7], [-0.6, 1.2], [0, 0], [0, 0], [0, 1.9], [0.7, 1.3], [1.4, 0.7], [0, 0], [2, 0], [1.3, -0.5], [0, 0], [0, 0], [-2.1, 2.3], [-3.1, 1.2], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 38, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [281.76, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 41, "ty": 4, "parent": 40, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [3.4, 2.9], [0, 6], [0, 0], [0, 0], [0, 0], [0, 0], [-0.7, -0.7], [-1.1, 0], [0, 0], [-1.2, 0.8], [0, 0], [0, 0], [1.9, -0.4], [2.1, 0]], "v": [[25.1, 77.5], [25.1, 77.5], [11, 73.1], [6, 59.7], [6, 59.7], [6, 23.1], [24.1, 23.1], [24.1, 59.6], [25.2, 62.7], [27.8, 63.8], [27.8, 63.8], [31.9, 62.6], [31.9, 62.6], [36.2, 75.2], [31.2, 76.9], [25.1, 77.5]], "o": [[0, 0], [-6, 0], [-3.4, -2.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.3], [0.7, 0.7], [0, 0], [1.5, 0], [0, 0], [0, 0], [-1.4, 0.8], [-1.9, 0.4], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33, 46.1], [0, 46.1], [0, 32.9], [33, 32.9], [33, 46.1]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 40, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [244.96, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 43, "ty": 4, "parent": 42, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [4, 2], [2.2, 3.4], [0, 4.3], [0, 0], [-2.2, 3.4], [-3.8, 1.9], [-4.7, 0], [0, 0], [-3.7, -1.7], [-2.2, -3.4], [0, -5], [0, 0], [0.1, -0.8], [0.1, -0.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, 1.2], [1.1, 0.7], [1.5, 0], [0, 0], [1.1, -0.7], [0.6, -1.2], [0, -1.7], [0, 0], [0, 0], [-0.7, -1.3], [-1.4, -0.7], [-2, 0], [0, 0], [-1.3, 0.5], [-1.4, 1.1], [0, 0], [0, 0], [3.1, -1.2], [4.4, 0]], "v": [[28.2, 77.5], [28.2, 77.5], [14.2, 74.5], [5, 66.4], [1.7, 54.8], [1.7, 54.8], [4.9, 43], [13.8, 35], [26.5, 32.1], [26.5, 32.1], [38.4, 34.7], [47.2, 42.3], [50.6, 54.8], [50.6, 54.8], [50.5, 56.9], [50.3, 59], [50.3, 59], [16.6, 59], [16.6, 50.3], [40.7, 50.3], [33.9, 52.6], [33, 48.2], [30.5, 45.4], [26.6, 44.4], [26.6, 44.4], [22.8, 45.4], [20.2, 48.2], [19.4, 52.6], [19.4, 52.6], [19.4, 55.3], [20.5, 60.1], [23.7, 63.1], [28.7, 64.1], [28.7, 64.1], [33.7, 63.3], [37.8, 60.9], [37.8, 60.9], [47.3, 70.4], [39.4, 75.7], [28.2, 77.5]], "o": [[0, 0], [-5.3, 0], [-4, -2], [-2.2, -3.4], [0, 0], [0, -4.4], [2.2, -3.4], [3.8, -1.9], [0, 0], [4.3, 0], [3.7, 1.7], [2.2, 3.4], [0, 0], [0, 0.6], [-0.1, 0.8], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.7], [-0.6, -1.2], [-1.1, -0.7], [0, 0], [-1.5, 0], [-1.1, 0.7], [-0.6, 1.2], [0, 0], [0, 0], [0, 1.9], [0.7, 1.3], [1.4, 0.7], [0, 0], [2, 0], [1.3, -0.5], [0, 0], [0, 0], [-2.1, 2.3], [-3.1, 1.2], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 42, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [192.72, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 45, "ty": 4, "parent": 44, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.1, 76.7], [16.4, 76.7], [-1.2, 32.9], [17.4, 32.9], [30.7, 68.4], [21.4, 68.4], [35.6, 32.9], [52.7, 32.9], [35.1, 76.7]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 44, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [141.2, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 47, "ty": 4, "parent": 46, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[22.1, 76.7], [4, 76.7], [4, 17.3], [22.1, 17.3], [22.1, 76.7]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 46, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [115.12, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 49, "ty": 4, "parent": 48, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [4, 2], [2.2, 3.4], [0, 4.3], [0, 0], [-2.2, 3.4], [-3.8, 1.9], [-4.7, 0], [0, 0], [-3.7, -1.7], [-2.2, -3.4], [0, -5], [0, 0], [0.1, -0.8], [0.1, -0.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, 1.2], [1.1, 0.7], [1.5, 0], [0, 0], [1.1, -0.7], [0.6, -1.2], [0, -1.7], [0, 0], [0, 0], [-0.7, -1.3], [-1.4, -0.7], [-2, 0], [0, 0], [-1.3, 0.5], [-1.4, 1.1], [0, 0], [0, 0], [3.1, -1.2], [4.4, 0]], "v": [[28.2, 77.5], [28.2, 77.5], [14.2, 74.5], [5, 66.4], [1.7, 54.8], [1.7, 54.8], [4.9, 43], [13.8, 35], [26.5, 32.1], [26.5, 32.1], [38.4, 34.7], [47.2, 42.3], [50.6, 54.8], [50.6, 54.8], [50.5, 56.9], [50.3, 59], [50.3, 59], [16.6, 59], [16.6, 50.3], [40.7, 50.3], [33.9, 52.6], [33, 48.2], [30.5, 45.4], [26.6, 44.4], [26.6, 44.4], [22.8, 45.4], [20.2, 48.2], [19.4, 52.6], [19.4, 52.6], [19.4, 55.3], [20.5, 60.1], [23.7, 63.1], [28.7, 64.1], [28.7, 64.1], [33.7, 63.3], [37.8, 60.9], [37.8, 60.9], [47.3, 70.4], [39.4, 75.7], [28.2, 77.5]], "o": [[0, 0], [-5.3, 0], [-4, -2], [-2.2, -3.4], [0, 0], [0, -4.4], [2.2, -3.4], [3.8, -1.9], [0, 0], [4.3, 0], [3.7, 1.7], [2.2, 3.4], [0, 0], [0, 0.6], [-0.1, 0.8], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.7], [-0.6, -1.2], [-1.1, -0.7], [0, 0], [-1.5, 0], [-1.1, 0.7], [-0.6, 1.2], [0, 0], [0, 0], [0, 1.9], [0.7, 1.3], [1.4, 0.7], [0, 0], [2, 0], [1.3, -0.5], [0, 0], [0, 0], [-2.1, 2.3], [-3.1, 1.2], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 48, "ty": 3, "parent": 37, "ks": {"p": {"a": 0, "k": [62.88, 0]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 51, "ty": 4, "parent": 50, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[40.7, 76.7], [22.2, 76.7], [-1.5, 20.7], [18.8, 20.7], [38.1, 68], [26, 68], [45.8, 20.7], [64.4, 20.7], [40.7, 76.7]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 50, "ty": 3, "parent": 37, "ks": {}, "ip": 0, "op": 121, "st": 0}, {"ind": 37, "ty": 3, "parent": 36, "ks": {"p": {"a": 1, "k": [{"t": 0, "s": [0, -96], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 37.38, "s": [0, -96], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 55.38, "s": [0, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 120, "s": [0, 0], "h": 1}]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 36, "ty": 3, "ks": {"p": {"a": 0, "k": [2, 79]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "66", "layers": [{"ind": 65, "ty": 0, "td": 1, "parent": 35, "ks": {"a": {"a": 0, "k": [96, 79]}}, "w": 718, "h": 236, "ip": 0, "op": 121, "st": 0, "refId": "63"}, {"ind": 54, "ty": 0, "tt": 1, "parent": 35, "ks": {"a": {"a": 0, "k": [2, 79]}}, "w": 335, "h": 157, "ip": 0, "op": 121, "st": 0, "refId": "52"}, {"ind": 35, "ty": 3, "ks": {"p": {"a": 0, "k": [2, 79]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "72", "layers": [{"ind": 71, "ty": 2, "parent": 70, "ks": {}, "ip": 0, "op": 121, "st": 0, "refId": "0"}, {"ind": 70, "ty": 3, "ks": {"s": {"a": 0, "k": [200, 200]}}, "ip": 0, "op": 121, "st": 0}]}, {"id": "75", "layers": [{"ind": 74, "ty": 0, "ks": {}, "w": 512, "h": 512, "ip": 0, "op": 121, "st": 0, "refId": "72"}]}], "fr": 60, "h": 720, "ip": 0, "layers": [{"ind": 34, "ty": 0, "parent": 4, "ks": {"a": {"a": 0, "k": [0, 77]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 43.38, "s": [0], "h": 1}, {"t": 43.38, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}]}, "p": {"a": 0, "k": [358, 0]}}, "w": 231, "h": 232, "ip": 0, "op": 121, "st": 0, "refId": "32"}, {"ind": 68, "ty": 0, "parent": 4, "ks": {"a": {"a": 0, "k": [2, 79]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 37.38, "s": [0], "h": 1}, {"t": 37.38, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}]}}, "w": 337, "h": 236, "ip": 0, "op": 121, "st": 0, "refId": "66"}, {"ind": 4, "ty": 3, "parent": 3, "ks": {"p": {"a": 0, "k": [1.34, -7.5]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 3, "ty": 3, "parent": 2, "ks": {"p": {"a": 0, "k": [343, 534]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 77, "ty": 0, "parent": 69, "ks": {"a": {"a": 0, "k": [256, 256]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 24, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [256, 307.2], "i": {"x": [1, 0], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 24, "s": [256, 256], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 120, "s": [256, 256], "h": 1}]}, "s": {"a": 1, "k": [{"t": 0, "s": [110, 110], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 16.8, "s": [110, 110], "i": {"x": [0, 0], "y": [1, 1]}, "o": {"x": [0.5, 0.5], "y": [0, 0]}}, {"t": 48, "s": [100, 100], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 120, "s": [100, 100], "h": 1}]}}, "w": 512, "h": 512, "ip": 0, "op": 121, "st": 0, "refId": "75"}, {"ind": 69, "ty": 3, "parent": 2, "ks": {"p": {"a": 1, "k": [{"t": 0, "s": [384, 156], "i": {"x": [0.785, 0.802], "y": [0.998, 0.5]}, "o": {"x": [0.822, 0.8], "y": [0, 0]}}, {"t": 15, "s": [385.5, 101.18447367601485], "i": {"x": [0.363, 0.351], "y": [0.647, 0.803]}, "o": {"x": [0.141, 0.13], "y": [0.001, 0.401]}}, {"t": 31.5, "s": [384.287, 52.08928926482342], "h": 1}, {"t": 31.5, "s": [384.287, 52.08928925621112], "i": {"x": [0.647, 0.585], "y": [1, 1]}, "o": {"x": [0.309, 0.258], "y": [0.725, 0.631]}}, {"t": 48, "s": [384, 46], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 120, "s": [384, 46], "h": 1}]}}, "ip": 0, "op": 121, "st": 0}, {"ind": 2, "ty": 3, "parent": 1, "ks": {}, "ip": 0, "op": 121, "st": 0}, {"ind": 78, "ty": 4, "parent": 1, "ks": {}, "ip": 0, "op": 121, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [640, 360]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [1280, 720]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}}]}, {"ind": 1, "ty": 3, "ks": {}, "ip": 0, "op": 121, "st": 0}], "meta": {"g": "https://jitter.video"}, "op": 120, "v": "5.7.4", "w": 1280}