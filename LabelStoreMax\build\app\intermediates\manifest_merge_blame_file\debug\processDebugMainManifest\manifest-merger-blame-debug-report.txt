1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37
38    <queries>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
39        <intent>
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
41        </intent>
42        <!-- Added to check the default browser that will host the AuthFlow. -->
43        <intent>
43-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
45
46            <data android:scheme="http" />
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
47        </intent>
48
49        <package android:name="com.facebook.katana" /> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
50        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
51        <package android:name="com.google.android.apps.maps" />
51-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:7-61
51-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:16-59
52    </queries>
53
54    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
54-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
54-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
55    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
55-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
55-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
56    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
56-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
56-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
57    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
57-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
57-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
58-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
58-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
59-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
59-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
60    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
60-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
60-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
61    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
62
63    <uses-feature
63-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:22:5-24:33
64        android:glEsVersion="0x00020000"
64-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:23:8-40
65        android:required="true" />
65-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:24:8-31
66
67    <permission
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
68        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
72    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
72-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
72-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
73
74    <application
74-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:18:5-76:19
75        android:name="com.velvete.ly.MainApplication"
75-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:19:9-54
76        android:allowBackup="false"
76-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:21:9-36
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="true"
80        android:fullBackupContent="false"
80-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:22:9-42
81        android:icon="@mipmap/ic_launcher"
81-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:24:9-43
82        android:label="Velvete Store"
82-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:20:9-38
83        android:supportsRtl="true"
83-->[com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
84        android:usesCleartextTraffic="true" >
84-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:23:9-44
85        <activity
85-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:25:9-43:20
86            android:name="com.velvete.ly.MainActivity"
86-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:26:13-55
87            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
87-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:29:13-163
88            android:exported="true"
88-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:33:13-36
89            android:hardwareAccelerated="true"
89-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:30:13-47
90            android:launchMode="singleTop"
90-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:27:13-43
91            android:screenOrientation="portrait"
91-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:31:13-49
92            android:theme="@style/LaunchTheme"
92-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:28:13-47
93            android:windowSoftInputMode="adjustResize" >
93-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:32:13-55
94            <meta-data
94-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:34:13-37:17
95                android:name="io.flutter.embedding.android.NormalTheme"
95-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:35:15-70
96                android:resource="@style/NormalTheme" />
96-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:36:15-52
97
98            <intent-filter>
98-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:39:13-42:29
99                <action android:name="android.intent.action.MAIN" />
99-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
99-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:17-76
101-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:27-74
102            </intent-filter>
103        </activity>
104
105        <meta-data
105-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:44:9-46:33
106            android:name="flutterEmbedding"
106-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:45:13-44
107            android:value="2" />
107-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:46:13-30
108        <meta-data
108-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:47:9-49:71
109            android:name="com.google.android.geo.API_KEY"
109-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:48:13-58
110            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
110-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:49:13-68
111
112        <!-- Facebook Configuration - TEMPORARILY DISABLED -->
113        <!--
114        <meta-data
115            android:name="com.facebook.sdk.ApplicationId"
116            android:value="@string/facebook_app_id"/>
117        <meta-data
118            android:name="com.facebook.sdk.ClientToken"
119            android:value="@string/facebook_client_token"/>
120
121        Facebook Activity for Login
122        <activity
123            android:name="com.facebook.FacebookActivity"
124            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
125            android:label="@string/app_name" />
126        <activity
127            android:name="com.facebook.CustomTabActivity"
128            android:exported="true">
129            <intent-filter>
130                <action android:name="android.intent.action.VIEW" />
131                <category android:name="android.intent.category.DEFAULT" />
132                <category android:name="android.intent.category.BROWSABLE" />
133                <data android:scheme="@string/fb_login_protocol_scheme" />
134            </intent-filter>
135        </activity>
136        -->
137        <service
137-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
138            android:name="com.baseflow.geolocator.GeolocatorLocationService"
138-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
139            android:enabled="true"
139-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
140            android:exported="false"
140-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
141            android:foregroundServiceType="location" />
141-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
142
143        <provider
143-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
144            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
144-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
145            android:authorities="com.velvete.ly.flutter.image_provider"
145-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
146            android:exported="false"
146-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
147            android:grantUriPermissions="true" >
147-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
148            <meta-data
148-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
149                android:name="android.support.FILE_PROVIDER_PATHS"
149-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
150                android:resource="@xml/flutter_image_picker_file_paths" />
150-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
151        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
152        <service
152-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
153            android:name="com.google.android.gms.metadata.ModuleDependencies"
153-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
154            android:enabled="false"
154-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
155            android:exported="false" >
155-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
156            <intent-filter>
156-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
157                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
157-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
157-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
158            </intent-filter>
159
160            <meta-data
160-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
161                android:name="photopicker_activity:0:required"
161-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
162                android:value="" />
162-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
163        </service>
164
165        <activity
165-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
166            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
166-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
167            android:exported="false"
167-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
168            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
168-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
169
170        <service
170-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
171            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
171-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
172            android:exported="false"
172-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
173            android:permission="android.permission.BIND_JOB_SERVICE" />
173-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
174        <service
174-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
175-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
176            android:exported="false" >
176-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
177            <intent-filter>
177-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
178                <action android:name="com.google.firebase.MESSAGING_EVENT" />
178-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
178-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
179            </intent-filter>
180        </service>
181
182        <receiver
182-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
183-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
184            android:exported="true"
184-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
185            android:permission="com.google.android.c2dm.permission.SEND" >
185-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
186            <intent-filter>
186-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
187                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
188            </intent-filter>
189        </receiver>
190
191        <service
191-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
192            android:name="com.google.firebase.components.ComponentDiscoveryService"
192-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
193            android:directBootAware="true"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
194            android:exported="false" >
194-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
195            <meta-data
195-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
196                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
196-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
198            <meta-data
198-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
199                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
199-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
201            <meta-data
201-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
202                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
202-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
204            <meta-data
204-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
205                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
205-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
207            <meta-data
207-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
208                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
208-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
210            <meta-data
210-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
211                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
211-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
213            <meta-data
213-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
214                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
214-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
216            <meta-data
216-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
217                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
219            <meta-data
219-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
220                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
220-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
222        </service>
223
224        <provider
224-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
225            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
225-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
226            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
226-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
227            android:exported="false"
227-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
228            android:initOrder="99" />
228-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
229
230        <activity
230-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
231            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
231-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
232            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
232-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
233            android:exported="false"
233-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
234            android:theme="@style/AppTheme" />
234-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
235        <activity
235-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
236            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
236-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
237            android:exported="false"
237-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
238            android:theme="@style/ThemeTransparent" />
238-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
239        <activity
239-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
240            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
240-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
241            android:exported="false"
241-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
242            android:theme="@style/ThemeTransparent" />
242-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
243        <activity
243-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
244            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
244-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
245            android:exported="false"
245-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
246            android:launchMode="singleInstance"
246-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
247            android:theme="@style/ThemeTransparent" />
247-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
248        <activity
248-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
249            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
249-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
250            android:exported="false"
250-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
251            android:launchMode="singleInstance"
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
252            android:theme="@style/ThemeTransparent" />
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
253
254        <receiver
254-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
255            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
255-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
256            android:enabled="true"
256-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
257            android:exported="false" />
257-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
258
259        <meta-data
259-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
260            android:name="io.flutter.embedded_views_preview"
260-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
261            android:value="true" />
261-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
262
263        <activity
263-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
264            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
264-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
265            android:exported="true"
265-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
266            android:launchMode="singleTask" >
266-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
267            <intent-filter>
267-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
268                <action android:name="android.intent.action.VIEW" />
268-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
268-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
269
270                <category android:name="android.intent.category.DEFAULT" />
270-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
270-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
271                <category android:name="android.intent.category.BROWSABLE" />
271-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
271-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
272
273                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
274                <data
274-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
275                    android:host="link-accounts"
276                    android:pathPrefix="/com.velvete.ly/authentication_return"
277                    android:scheme="stripe-auth" />
277-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
278
279                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
280                <data
280-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
281                    android:host="link-native-accounts"
282                    android:pathPrefix="/com.velvete.ly/authentication_return"
283                    android:scheme="stripe-auth" />
283-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
284
285                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
286                <data
286-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
287                    android:host="link-accounts"
288                    android:path="/com.velvete.ly/success"
289                    android:scheme="stripe-auth" />
289-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
290                <data
290-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
291                    android:host="link-accounts"
292                    android:path="/com.velvete.ly/cancel"
293                    android:scheme="stripe-auth" />
293-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
294
295                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
296                <data
296-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
297                    android:host="native-redirect"
298                    android:pathPrefix="/com.velvete.ly"
299                    android:scheme="stripe-auth" />
299-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
300
301                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
302                <data
302-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
303                    android:host="auth-redirect"
304                    android:pathPrefix="/com.velvete.ly"
305                    android:scheme="stripe" />
305-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
306            </intent-filter>
307        </activity>
308        <activity
308-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
309            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
309-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
310            android:exported="false"
310-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
311            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
311-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
312        <activity
312-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
313            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
313-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
314            android:exported="false"
314-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
315            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
315-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
316            android:windowSoftInputMode="adjustResize" />
316-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
317        <activity
317-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
318            android:name="com.facebook.FacebookActivity"
318-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
319            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
319-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
320            android:theme="@style/com_facebook_activity_theme" />
320-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
321        <activity android:name="com.facebook.CustomTabMainActivity" />
321-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
321-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
322        <activity
322-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
323            android:name="com.facebook.CustomTabActivity"
323-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
324            android:exported="true" >
324-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
325            <intent-filter>
325-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
326                <action android:name="android.intent.action.VIEW" />
326-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
326-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
327
328                <category android:name="android.intent.category.DEFAULT" />
328-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
328-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
329                <category android:name="android.intent.category.BROWSABLE" />
329-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
329-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
330
331                <data
331-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
332                    android:host="cct.com.velvete.ly"
333                    android:scheme="fbconnect" />
333-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
334            </intent-filter>
335        </activity>
336        <activity
336-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
337            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
337-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
338            android:exported="false"
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
339            android:theme="@style/StripePaymentSheetDefaultTheme" />
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
340        <activity
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
341            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
342            android:exported="false"
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
343            android:theme="@style/StripePaymentSheetDefaultTheme" />
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
344        <activity
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
345            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
345-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
346            android:exported="false"
346-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
347            android:theme="@style/StripePaymentSheetDefaultTheme" />
347-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
348        <activity
348-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
349            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
349-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
350            android:exported="false"
350-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
351            android:theme="@style/StripePaymentSheetDefaultTheme" />
351-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
352        <activity
352-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
353            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
353-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
354            android:exported="false"
354-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
355            android:theme="@style/StripePaymentSheetDefaultTheme" />
355-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
356        <activity
356-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
357            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
357-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
358            android:exported="false"
358-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
359            android:theme="@style/StripePaymentSheetDefaultTheme" />
359-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
360        <activity
360-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
361            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
361-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
362            android:exported="false"
362-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
363            android:theme="@style/StripePaymentSheetDefaultTheme" />
363-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
364        <activity
364-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
365            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
365-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
366            android:exported="false"
366-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
367            android:theme="@style/StripePayLauncherDefaultTheme" />
367-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
368        <activity
368-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
369            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
369-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
370            android:theme="@style/StripePaymentSheetDefaultTheme" />
370-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
371        <activity
371-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
372            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
372-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
373            android:theme="@style/StripePaymentSheetDefaultTheme" />
373-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
374        <activity
374-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
375            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
375-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
376            android:theme="@style/StripePaymentSheetDefaultTheme" />
376-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
377        <activity
377-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
378            android:name="com.stripe.android.link.LinkActivity"
378-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
379            android:autoRemoveFromRecents="true"
379-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
380            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
380-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
381            android:exported="false"
381-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
382            android:label="@string/stripe_link"
382-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
383            android:theme="@style/StripeLinkBaseTheme"
383-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
384            android:windowSoftInputMode="adjustResize" />
384-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
385        <activity
385-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
386            android:name="com.stripe.android.link.LinkForegroundActivity"
386-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
387            android:autoRemoveFromRecents="true"
387-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
388            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
388-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
389            android:launchMode="singleTop"
389-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
390            android:theme="@style/StripeTransparentTheme" />
390-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
391        <activity
391-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
392            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
392-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
393            android:autoRemoveFromRecents="true"
393-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
394            android:exported="true"
394-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
395            android:launchMode="singleInstance"
395-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
396            android:theme="@style/StripeTransparentTheme" >
396-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
397            <intent-filter>
397-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
398                <action android:name="android.intent.action.VIEW" />
398-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
398-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
399
400                <category android:name="android.intent.category.DEFAULT" />
400-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
400-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
401                <category android:name="android.intent.category.BROWSABLE" />
401-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
401-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
402
403                <data
403-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
404                    android:host="complete"
405                    android:path="/com.velvete.ly"
406                    android:scheme="link-popup" />
406-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
407            </intent-filter>
408        </activity>
409        <activity
409-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
410            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
410-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
411            android:exported="false"
411-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
412            android:theme="@style/StripePaymentSheetDefaultTheme" />
412-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
413        <activity
413-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
414            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
414-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
415            android:exported="false"
415-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
416            android:theme="@style/StripeDefaultTheme" />
416-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
417        <activity
417-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
418            android:name="com.stripe.android.view.PaymentRelayActivity"
418-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
419            android:exported="false"
419-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
420            android:theme="@style/StripeTransparentTheme" />
420-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
421        <!--
422        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
423        launched the browser Activity will also handle the return URL deep link.
424        -->
425        <activity
425-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
426            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
426-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
427            android:exported="false"
427-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
428            android:launchMode="singleTask"
428-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
429            android:theme="@style/StripeTransparentTheme" />
429-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
430        <activity
430-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
431            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
431-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
432            android:exported="true"
432-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
433            android:launchMode="singleTask"
433-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
434            android:theme="@style/StripeTransparentTheme" >
434-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
435            <intent-filter>
435-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
436                <action android:name="android.intent.action.VIEW" />
436-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
436-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
437
438                <category android:name="android.intent.category.DEFAULT" />
438-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
438-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
439                <category android:name="android.intent.category.BROWSABLE" />
439-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
439-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
440
441                <!-- Must match `DefaultReturnUrl#value`. -->
442                <data
442-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
443                    android:host="payment_return_url"
444                    android:path="/com.velvete.ly"
445                    android:scheme="stripesdk" />
445-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
446            </intent-filter>
447        </activity>
448        <activity
448-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
449            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
449-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
450            android:exported="false"
450-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
451            android:theme="@style/StripeDefaultTheme" />
451-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
452        <activity
452-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
453            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
453-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
454            android:exported="false"
454-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
455            android:theme="@style/StripeGooglePayDefaultTheme" />
455-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
456        <activity
456-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
457            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
457-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
458            android:exported="false"
458-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
459            android:theme="@style/StripeGooglePayDefaultTheme" />
459-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
460        <activity
460-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
461            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
461-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
462            android:exported="false"
462-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
463            android:theme="@style/StripePayLauncherDefaultTheme" />
463-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
464        <activity
464-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
465            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
465-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
466            android:exported="false"
466-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
467            android:theme="@style/StripeTransparentTheme" />
467-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
468
469        <service
469-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
470            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
470-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
471            android:enabled="true"
471-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
472            android:exported="false" >
472-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
473            <meta-data
473-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
474                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
474-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
475                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
475-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
476        </service>
477
478        <activity
478-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
479            android:name="androidx.credentials.playservices.HiddenActivity"
479-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
480            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
480-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
481            android:enabled="true"
481-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
482            android:exported="false"
482-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
483            android:fitsSystemWindows="true"
483-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
484            android:theme="@style/Theme.Hidden" >
484-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
485        </activity>
486        <activity
486-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
487            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
487-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
488            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
488-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
489            android:enabled="true"
489-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
490            android:exported="false"
490-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
491            android:fitsSystemWindows="true"
491-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
492            android:theme="@style/Theme.Hidden" >
492-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
493        </activity>
494
495        <uses-library
495-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
496            android:name="androidx.window.extensions"
496-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
497            android:required="false" />
497-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
498        <uses-library
498-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
499            android:name="androidx.window.sidecar"
499-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
500            android:required="false" />
500-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
501
502        <activity
502-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
503            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
503-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
504            android:exported="false"
504-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
505            android:theme="@style/Stripe3DS2Theme" />
505-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
506        <activity
506-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
507            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
507-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
508            android:excludeFromRecents="true"
508-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
509            android:exported="false"
509-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
510            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
510-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
511        <!--
512            Service handling Google Sign-In user revocation. For apps that do not integrate with
513            Google Sign-In, this service will never be started.
514        -->
515        <service
515-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
516            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
516-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
517            android:exported="true"
517-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
518            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
518-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
519            android:visibleToInstantApps="true" />
519-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
520        <!--
521         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
522         with the application context. This config is merged in with the host app's manifest,
523         but there can only be one provider with the same authority activated at any given
524         point; so if the end user has two or more different apps that use Facebook SDK, only the
525         first one will be able to use the provider. To work around this problem, we use the
526         following placeholder in the authority to identify each host application as if it was
527         a completely different provider.
528        -->
529        <provider
529-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
530            android:name="com.facebook.internal.FacebookInitProvider"
530-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
531            android:authorities="com.velvete.ly.FacebookInitProvider"
531-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
532            android:exported="false" />
532-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
533
534        <receiver
534-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
535            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
535-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
536            android:exported="false" >
536-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
537            <intent-filter>
537-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
538                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
538-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
538-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
539            </intent-filter>
540        </receiver>
541        <receiver
541-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
542            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
542-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
543            android:exported="false" >
543-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
544            <intent-filter>
544-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
545                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
545-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
545-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
546            </intent-filter>
547        </receiver>
548        <receiver
548-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
549            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
549-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
550            android:exported="true"
550-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
551            android:permission="com.google.android.c2dm.permission.SEND" >
551-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
552            <intent-filter>
552-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
553                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
553-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
553-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
554            </intent-filter>
555
556            <meta-data
556-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
557                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
557-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
558                android:value="true" />
558-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
559        </receiver>
560        <!--
561             FirebaseMessagingService performs security checks at runtime,
562             but set to not exported to explicitly avoid allowing another app to call it.
563        -->
564        <service
564-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
565            android:name="com.google.firebase.messaging.FirebaseMessagingService"
565-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
566            android:directBootAware="true"
566-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
567            android:exported="false" >
567-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
568            <intent-filter android:priority="-500" >
568-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
569                <action android:name="com.google.firebase.MESSAGING_EVENT" />
569-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
569-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
570            </intent-filter>
571        </service> <!-- Needs to be explicitly declared on P+ -->
572        <uses-library
572-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:7-86
573            android:name="org.apache.http.legacy"
573-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:21-58
574            android:required="false" />
574-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fce470fecd9e9cb3d8012a94e0b0f542\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:59-83
575
576        <activity
576-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
577            android:name="com.google.android.gms.common.api.GoogleApiActivity"
577-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
578            android:exported="false"
578-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
579            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
579-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
580
581        <provider
581-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
582            android:name="com.google.firebase.provider.FirebaseInitProvider"
582-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
583            android:authorities="com.velvete.ly.firebaseinitprovider"
583-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
584            android:directBootAware="true"
584-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
585            android:exported="false"
585-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
586            android:initOrder="100" />
586-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
587        <provider
587-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
588            android:name="androidx.startup.InitializationProvider"
588-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
589            android:authorities="com.velvete.ly.androidx-startup"
589-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
590            android:exported="false" >
590-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
591            <meta-data
591-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
592                android:name="androidx.emoji2.text.EmojiCompatInitializer"
592-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
593                android:value="androidx.startup" />
593-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
594            <meta-data
594-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
595                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
595-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
596                android:value="androidx.startup" />
596-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
597            <meta-data
597-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
598                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
598-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
599                android:value="androidx.startup" />
599-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
600        </provider>
601
602        <meta-data
602-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
603            android:name="com.google.android.gms.version"
603-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
604            android:value="@integer/google_play_services_version" />
604-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
605
606        <receiver
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
607            android:name="androidx.profileinstaller.ProfileInstallReceiver"
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
608            android:directBootAware="false"
608-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
609            android:enabled="true"
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
610            android:exported="true"
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
611            android:permission="android.permission.DUMP" >
611-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
612            <intent-filter>
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
613                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
614            </intent-filter>
615            <intent-filter>
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
616                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
617            </intent-filter>
618            <intent-filter>
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
619                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
619-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
619-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
620            </intent-filter>
621            <intent-filter>
621-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
622                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
622-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
622-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
623            </intent-filter>
624        </receiver>
625
626        <service
626-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
627            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
627-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
628            android:exported="false" >
628-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
629            <meta-data
629-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
630                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
630-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
631                android:value="cct" />
631-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
632        </service>
633        <service
633-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
634            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
634-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
635            android:exported="false"
635-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
636            android:permission="android.permission.BIND_JOB_SERVICE" >
636-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
637        </service>
638
639        <receiver
639-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
640            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
640-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
641            android:exported="false" />
641-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
642
643        <meta-data
643-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
644            android:name="aia-compat-api-min-version"
644-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
645            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
645-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
646        <activity
646-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
647            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
647-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
648            android:exported="false"
648-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
649            android:stateNotNeeded="true"
649-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
650            android:theme="@style/Theme.PlayCore.Transparent" />
650-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
651    </application>
652
653</manifest>
