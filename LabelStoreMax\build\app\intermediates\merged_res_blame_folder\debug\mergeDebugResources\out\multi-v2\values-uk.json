{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,235,315,407,496,609,729,818,907,1007,1092,1177,1277", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "138,230,310,402,491,604,724,813,902,1002,1087,1172,1272,1386"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5101,5189,5281,5361,5453,5542,5655,5775,5864,5953,6053,6138,6223,6323", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "5184,5276,5356,5448,5537,5650,5770,5859,5948,6048,6133,6218,6318,6432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "191", "startColumns": "4", "startOffsets": "17676", "endColumns": "79", "endOffsets": "17751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "88,97,178,182,192,198,199", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8770,9544,16564,16868,17756,18395,18480", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "8839,9627,16640,17005,17920,18475,18558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "87,91,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8663,9056,10136,10263,10394,10535,10646,10773,10907,11006,11136,11268", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "8765,9144,10258,10389,10530,10641,10768,10902,11001,11131,11263,11388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,203", "endColumns": "76,70,77", "endOffsets": "127,198,276"}, "to": {"startLines": "54,93,98", "startColumns": "4,4,4", "startOffsets": "5024,9250,9632", "endColumns": "76,70,77", "endOffsets": "5096,9316,9705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,81", "endOffsets": "258,340"}, "to": {"startLines": "113,200", "startColumns": "4,4", "startOffsets": "11393,18563", "endColumns": "60,85", "endOffsets": "11449,18644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "18198,18295", "endColumns": "96,99", "endOffsets": "18290,18390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,95,96,99,114,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,181,186,187,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3382,3460,3538,3626,3734,4552,4648,4764,9405,9477,9710,11454,11691,11754,11842,11904,11971,12029,12100,12159,12213,12327,12387,12450,12504,12577,12696,12782,12858,12949,13030,13113,13252,13337,13424,13557,13645,13723,13780,13831,13897,13969,14045,14116,14199,14272,14349,14431,14505,14614,14704,14783,14874,14970,15044,15125,15220,15274,15356,15422,15509,15595,15657,15721,15784,15857,15964,16074,16172,16278,16339,16786,17265,17350,17498", "endLines": "7,37,38,39,40,41,49,50,51,95,96,99,114,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,181,186,187,189", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "419,3455,3533,3621,3729,3820,4643,4759,4842,9472,9539,9796,11515,11749,11837,11899,11966,12024,12095,12154,12208,12322,12382,12445,12499,12572,12691,12777,12853,12944,13025,13108,13247,13332,13419,13552,13640,13718,13775,13826,13892,13964,14040,14111,14194,14267,14344,14426,14500,14609,14699,14778,14869,14965,15039,15120,15215,15269,15351,15417,15504,15590,15652,15716,15779,15852,15959,16069,16167,16273,16334,16389,16863,17345,17421,17570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,190", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3825,3925,4027,4128,4229,4334,4439,17575", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3920,4022,4123,4224,4329,4434,4547,17671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3153,3263", "endColumns": "109,118", "endOffsets": "3258,3377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "7448", "endColumns": "145", "endOffsets": "7589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "52,53,90,92,94,115,116,176,177,179,180,183,184,188,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4847,4940,8954,9149,9321,11520,11602,16394,16482,16645,16715,17010,17095,17426,17925,18005,18075", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "4935,5019,9051,9245,9400,11597,11686,16477,16559,16710,16781,17090,17178,17493,18000,18070,18193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6437,6545,6708,6835,6945,7099,7228,7343,7594,7762,7868,8030,8155,8302,8444,8514,8575", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "6540,6703,6830,6940,7094,7223,7338,7443,7757,7863,8025,8150,8297,8439,8509,8570,8658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1135,1217,1308,1401,1496,1590,1690,1783,1878,1973,2064,2155,2254,2360,2466,2564,2671,2778,2883,3053,17183", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1130,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,3148,17260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "89,100,101,102", "startColumns": "4,4,4,4", "startOffsets": "8844,9801,9908,10028", "endColumns": "109,106,119,107", "endOffsets": "8949,9903,10023,10131"}}]}]}