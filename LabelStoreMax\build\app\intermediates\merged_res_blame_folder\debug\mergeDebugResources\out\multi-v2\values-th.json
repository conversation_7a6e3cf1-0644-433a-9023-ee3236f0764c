{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3190,3263,3335,3418,3503,4304,4403,4516,10227,10295,10523,12186,12426,12486,12573,12639,12704,12765,12829,12890,12944,13045,13106,13166,13220,13290,13401,13488,13565,13652,13734,13815,13958,14037,14119,14251,14343,14421,14475,14528,14594,14664,14742,14813,14893,14965,15043,15112,15181,15279,15361,15449,15542,15636,15710,15779,15874,15926,16009,16077,16162,16250,16312,16376,16439,16509,16609,16705,16802,16895,16953,17390,17863,17945,18093", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3258,3330,3413,3498,3584,4398,4511,4591,10290,10360,10608,12251,12481,12568,12634,12699,12760,12824,12885,12939,13040,13101,13161,13215,13285,13396,13483,13560,13647,13729,13810,13953,14032,14114,14246,14338,14416,14470,14523,14589,14659,14737,14808,14888,14960,15038,15107,15176,15274,15356,15444,15537,15631,15705,15774,15869,15921,16004,16072,16157,16245,16307,16371,16434,16504,16604,16700,16797,16890,16948,17005,17462,17940,18015,18164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,259,372,499,625,751,871,995,1090,1219,1348", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "155,254,367,494,620,746,866,990,1085,1214,1343,1458"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9487,9862,10921,11034,11161,11287,11413,11533,11657,11752,11881,12010", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "9587,9956,11029,11156,11282,11408,11528,11652,11747,11876,12005,12120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4759,10062,10451", "endColumns": "71,76,71", "endOffsets": "4826,10134,10518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "95,104,185,189,512,518,519", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9592,10365,17179,17467,47736,48364,48444", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "9657,10446,17254,17601,47899,48439,48517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7363,7469,7616,7739,7846,7982,8106,8225,8462,8606,8711,8858,8980,9120,9271,9335,9403", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "7464,7611,7734,7841,7977,8101,8220,8328,8601,8706,8853,8975,9115,9266,9330,9398,9482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,513,514,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4682,9765,9961,10139,12256,12341,17010,17096,17259,17324,17606,17692,18020,47904,47982,48049", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4677,4754,9857,10057,10222,12336,12421,17091,17174,17319,17385,17687,17776,18088,47977,48044,48167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3589,3685,3788,3886,3984,4087,4192,18169", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3680,3783,3881,3979,4082,4187,4299,18265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "120,520", "startColumns": "4,4", "startOffsets": "12125,48522", "endColumns": "60,74", "endOffsets": "12181,48592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,326,428,643,692,760,849,920,1132,1192,1280,1357,1412,1476,1547,1634,1942,2010,2080,2133,2186,2263,2378,2474,2542,2619,2693,2777,2845,2941,3205,3279,3357,3416,3478,3539,3601,3668,3743,3838,3937,4020,4139,4242,4315,4394,4497,4700,4912,5029,5158,5212,5808,5905,5967", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "161,321,423,638,687,755,844,915,1127,1187,1275,1352,1407,1471,1542,1629,1937,2005,2075,2128,2181,2258,2373,2469,2537,2614,2688,2772,2840,2936,3200,3274,3352,3411,3473,3534,3596,3663,3738,3833,3932,4015,4134,4237,4310,4389,4492,4695,4907,5024,5153,5207,5803,5900,5962,6017"}, "to": {"startLines": "274,275,276,278,282,283,284,285,286,287,288,304,307,309,313,314,319,325,326,334,344,348,349,350,351,352,358,361,366,368,369,370,371,374,376,377,381,385,386,388,390,402,427,428,429,430,431,449,456,457,458,459,461,462,463,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24231,24342,24502,24695,25618,25667,25735,25824,25895,26107,26167,27927,28190,28367,28661,28732,29092,29782,29850,30374,31256,31532,31609,31724,31820,31888,32421,32652,33073,33211,33307,33571,33645,33890,34078,34140,34434,35023,35090,35253,35444,37031,39319,39438,39541,39614,39693,41342,41990,42202,42319,42448,42575,43171,43268,44921", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "24337,24497,24599,24905,25662,25730,25819,25890,26102,26162,26250,27999,28240,28426,28727,28814,29395,29845,29915,30422,31304,31604,31719,31815,31883,31960,32490,32731,33136,33302,33566,33640,33718,33944,34135,34196,34491,35085,35160,35343,35538,37109,39433,39536,39609,39688,39791,41540,42197,42314,42443,42497,43166,43263,43325,44971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2964,3076", "endColumns": "111,113", "endOffsets": "3071,3185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3302,3370,3436,4010,4563,4638,4744,4838,4893,4980,5064,5130,5214,5297,5354,5428,5477,5560,5658,5727,5798,5865,5931,5976,6051,6145,6219,6269,6315,6385,6443,6508,6675,6828,6944,7009,7087,7157,7244,7321,7401,7470,7564,7634,7742,7822,7908,7958,8068,8116,8173,8246,8308,8376,8442,8514,8597,8661,8710", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3297,3365,3431,4005,4558,4633,4739,4833,4888,4975,5059,5125,5209,5292,5349,5423,5472,5555,5653,5722,5793,5860,5926,5971,6046,6140,6214,6264,6310,6380,6438,6503,6670,6823,6939,7004,7082,7152,7239,7316,7396,7465,7559,7629,7737,7817,7903,7953,8063,8111,8168,8241,8303,8371,8437,8509,8592,8656,8705,8784"}, "to": {"startLines": "198,199,200,201,202,203,204,208,209,210,211,214,216,217,219,224,228,243,246,247,248,250,251,252,254,258,259,260,261,262,263,264,265,266,267,269,272,273,279,280,281,292,293,294,295,296,297,298,299,300,301,302,303,310,311,312,315,316,317,318,322,327,328,329,330,331,336,337,338,339,340,341,345,346,356,357,359,360,364,365,367,372,379,380,451,452,453,455,460,473,474,475,476,477,478,479,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18270,18339,18417,18479,18547,18624,18686,18981,19036,19111,19187,19413,19605,19715,19877,20243,20544,21576,21847,21949,22020,22185,22279,22367,22542,22864,22963,23041,23147,23217,23306,23392,23475,23556,23655,23799,24036,24137,24910,24978,25044,26509,27062,27137,27243,27337,27392,27479,27563,27629,27713,27796,27853,28431,28480,28563,28819,28888,28959,29026,29541,29920,29995,30089,30163,30213,30491,30561,30619,30684,30851,31004,31309,31374,32264,32334,32495,32572,32910,32979,33141,33723,34268,34348,41630,41680,41790,41933,42502,44457,44519,44587,44653,44725,44808,44872,46207", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "18334,18412,18474,18542,18619,18681,18749,19031,19106,19182,19261,19518,19710,19804,19965,20326,20631,21677,21944,22015,22114,22274,22362,22478,22624,22958,23036,23142,23212,23301,23387,23470,23551,23650,23718,23868,24132,24226,24973,25039,25613,27057,27132,27238,27332,27387,27474,27558,27624,27708,27791,27848,27922,28475,28558,28656,28883,28954,29021,29087,29581,29990,30084,30158,30208,30254,30556,30614,30679,30846,30999,31115,31369,31447,32329,32416,32567,32647,32974,33068,33206,33826,34343,34429,41675,41785,41833,41985,42570,44514,44582,44648,44720,44803,44867,44916,46281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "213,223,225,226,227,231,239,242,245,249,253,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19331,20183,20331,20395,20480,20763,21308,21498,21773,22119,22483,22793", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "19408,20238,20390,20475,20539,20830,21362,21571,21842,22180,22537,22859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,332,485,602,688,774,851,940,1034,1151,1263,1357,1451,1559,1683,1762,1843,2032,2128,2234,2353,2463", "endColumns": "147,128,152,116,85,85,76,88,93,116,111,93,93,107,123,78,80,188,95,105,118,109,123", "endOffsets": "198,327,480,597,683,769,846,935,1029,1146,1258,1352,1446,1554,1678,1757,1838,2027,2123,2229,2348,2458,2582"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4831,4979,5108,5261,5378,5464,5550,5627,5716,5810,5927,6039,6133,6227,6335,6459,6538,6619,6808,6904,7010,7129,7239", "endColumns": "147,128,152,116,85,85,76,88,93,116,111,93,93,107,123,78,80,188,95,105,118,109,123", "endOffsets": "4974,5103,5256,5373,5459,5545,5622,5711,5805,5922,6034,6128,6222,6330,6454,6533,6614,6803,6899,7005,7124,7234,7358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "31452", "endColumns": "79", "endOffsets": "31527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "48172,48269", "endColumns": "96,94", "endOffsets": "48264,48359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,17781", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,17858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "212,215,218,220,221,222,229,230,232,233,234,235,236,237,238,240,241,244,255,256,268,270,271,305,306,320,332,333,335,342,343,353,354,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19266,19523,19809,19970,20055,20122,20636,20701,20835,20900,20973,21045,21114,21175,21244,21367,21434,21682,22629,22694,23723,23873,23951,28004,28120,29400,30259,30306,30427,31120,31179,31965,32054,32736,32825", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "19326,19600,19872,20050,20117,20178,20696,20758,20895,20968,21040,21109,21170,21239,21303,21429,21493,21768,22689,22788,23794,23946,24031,28115,28185,29445,30301,30369,30486,31174,31251,32049,32134,32820,32905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9662,10613,10712,10823", "endColumns": "102,98,110,97", "endOffsets": "9760,10707,10818,10916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8333", "endColumns": "128", "endOffsets": "8457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,373,468,548,627,749,840,943,1036,1161,1220,1349,1416,1601,1794,1943,2031,2127,2220,2321,2467,2571,2668,2901,3016,3142,3328,3520,3615,3726,3907,3998,4056,4119,4202,4294,4394,4465,4558,4659,4763,4945,5012,5078,5153,5266,5344,5430,5493,5567,5641,5710,5820,5911,6018,6109,6179,6258,6326,6411,6471,6571,6677,6780,6903,6968,7059,7155,7297,7366,7451,7546,7600,7657,7740,7840,8001,8242,8498,8596,8673,8757,8830,8935,9045,9131,9205,9275,9359,9429,9520,9637,9762,9833,9904,9961,10138,10210,10272,10351,10424,10498,10573,10695,10805,10905,10991,11068,11151,11216", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "119,202,277,368,463,543,622,744,835,938,1031,1156,1215,1344,1411,1596,1789,1938,2026,2122,2215,2316,2462,2566,2663,2896,3011,3137,3323,3515,3610,3721,3902,3993,4051,4114,4197,4289,4389,4460,4553,4654,4758,4940,5007,5073,5148,5261,5339,5425,5488,5562,5636,5705,5815,5906,6013,6104,6174,6253,6321,6406,6466,6566,6672,6775,6898,6963,7054,7150,7292,7361,7446,7541,7595,7652,7735,7835,7996,8237,8493,8591,8668,8752,8825,8930,9040,9126,9200,9270,9354,9424,9515,9632,9757,9828,9899,9956,10133,10205,10267,10346,10419,10493,10568,10690,10800,10900,10986,11063,11146,11211,11349"}, "to": {"startLines": "205,206,207,277,289,290,291,308,321,323,324,355,373,375,378,382,383,384,387,389,391,392,393,394,395,396,397,398,399,400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,450,454,464,465,466,467,468,469,470,471,472,481,482,483,484,485,486,487,488,489,490,491,492,493,494,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18754,18823,18906,24604,26255,26350,26430,28245,29450,29586,29689,32139,33831,33949,34201,34496,34681,34874,35165,35348,35543,35636,35737,35883,35987,36084,36317,36432,36558,36744,36936,37114,37225,37406,37497,37555,37618,37701,37793,37893,37964,38057,38158,38262,38444,38511,38577,38652,38765,38843,38929,38992,39066,39140,39209,39796,39887,39994,40085,40155,40234,40302,40387,40447,40547,40653,40756,40879,40944,41035,41131,41273,41545,41838,43330,43384,43441,43524,43624,43785,44026,44282,44380,44976,45060,45133,45238,45348,45434,45508,45578,45662,45732,45823,45940,46065,46136,46286,46343,46520,46592,46654,46733,46806,46880,46955,47077,47187,47287,47373,47450,47533,47598", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "18818,18901,18976,24690,26345,26425,26504,28362,29536,29684,29777,32259,33885,34073,34263,34676,34869,35018,35248,35439,35631,35732,35878,35982,36079,36312,36427,36553,36739,36931,37026,37220,37401,37492,37550,37613,37696,37788,37888,37959,38052,38153,38257,38439,38506,38572,38647,38760,38838,38924,38987,39061,39135,39204,39314,39882,39989,40080,40150,40229,40297,40382,40442,40542,40648,40751,40874,40939,41030,41126,41268,41337,41625,41928,43379,43436,43519,43619,43780,44021,44277,44375,44452,45055,45128,45233,45343,45429,45503,45573,45657,45727,45818,45935,46060,46131,46202,46338,46515,46587,46649,46728,46801,46875,46950,47072,47182,47282,47368,47445,47528,47593,47731"}}]}]}