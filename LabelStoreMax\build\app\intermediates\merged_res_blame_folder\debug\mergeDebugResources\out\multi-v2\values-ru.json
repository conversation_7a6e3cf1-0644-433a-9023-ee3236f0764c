{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3806,3904,4006,4107,4208,4313,4416,18966", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3899,4001,4102,4203,4308,4411,4528,19062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "52,53,99,101,103,124,125,185,186,188,189,192,193,197,522,523,524", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4859,4952,10220,10413,10596,12887,12969,17779,17867,18026,18097,18392,18476,18815,51432,51516,51586", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4947,5030,10313,10510,10683,12964,13054,17862,17944,18092,18162,18471,18558,18882,51511,51581,51704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "525,526", "startColumns": "4,4", "startOffsets": "51709,51797", "endColumns": "87,90", "endOffsets": "51792,51883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,299,408,514,597,680,822,931,1035,1110,1253,1310,1429,1502,1680,1893,2043,2134,2232,2327,2423,2626,2739,2836,3086,3184,3316,3557,3796,3901,4013,4216,4308,4370,4438,4529,4622,4725,4803,4902,4997,5101,5316,5390,5459,5534,5648,5732,5817,5888,5972,6046,6123,6271,6363,6471,6569,6643,6724,6793,6879,6942,7046,7181,7292,7426,7490,7583,7681,7819,7890,7982,8093,8152,8215,8317,8425,8611,8907,9218,9323,9396,9493,9566,9696,9814,9910,10004,10076,10162,10241,10331,10462,10597,10662,10738,10796,10982,11049,11112,11190,11262,11340,11423,11540,11664,11773,11862,11951,12039,12118", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "127,217,294,403,509,592,675,817,926,1030,1105,1248,1305,1424,1497,1675,1888,2038,2129,2227,2322,2418,2621,2734,2831,3081,3179,3311,3552,3791,3896,4008,4211,4303,4365,4433,4524,4617,4720,4798,4897,4992,5096,5311,5385,5454,5529,5643,5727,5812,5883,5967,6041,6118,6266,6358,6466,6564,6638,6719,6788,6874,6937,7041,7176,7287,7421,7485,7578,7676,7814,7885,7977,8088,8147,8210,8312,8420,8606,8902,9213,9318,9391,9488,9561,9691,9809,9905,9999,10071,10157,10236,10326,10457,10592,10657,10733,10791,10977,11044,11107,11185,11257,11335,11418,11535,11659,11768,11857,11946,12034,12113,12270"}, "to": {"startLines": "214,215,216,286,298,299,300,317,330,332,333,364,382,384,387,391,392,393,396,398,400,401,402,403,404,405,406,407,408,409,410,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,459,463,473,474,475,476,477,478,479,480,481,490,491,492,493,494,495,496,497,498,499,500,501,502,503,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20144,20221,20311,26139,28045,28151,28234,30243,31507,31665,31769,34378,36245,36384,36628,36942,37120,37333,37637,37835,38032,38127,38223,38426,38539,38636,38886,38984,39116,39357,39596,39802,39914,40117,40209,40271,40339,40430,40523,40626,40704,40803,40898,41002,41217,41291,41360,41435,41549,41633,41718,41789,41873,41947,42024,42648,42740,42848,42946,43020,43101,43170,43256,43319,43423,43558,43669,43803,43867,43960,44058,44196,44454,44789,46456,46515,46578,46680,46788,46974,47270,47581,47686,48310,48407,48480,48610,48728,48824,48918,48990,49076,49155,49245,49376,49511,49576,49726,49784,49970,50037,50100,50178,50250,50328,50411,50528,50652,50761,50850,50939,51027,51106", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "20216,20306,20383,26243,28146,28229,28312,30380,31611,31764,31839,34516,36297,36498,36696,37115,37328,37478,37723,37928,38122,38218,38421,38534,38631,38881,38979,39111,39352,39591,39696,39909,40112,40204,40266,40334,40425,40518,40621,40699,40798,40893,40997,41212,41286,41355,41430,41544,41628,41713,41784,41868,41942,42019,42167,42735,42843,42941,43015,43096,43165,43251,43314,43418,43553,43664,43798,43862,43955,44053,44191,44262,44541,44895,46510,46573,46675,46783,46969,47265,47576,47681,47754,48402,48475,48605,48723,48819,48913,48985,49071,49150,49240,49371,49506,49571,49647,49779,49965,50032,50095,50173,50245,50323,50406,50523,50647,50756,50845,50934,51022,51101,51258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "86", "startColumns": "4", "startOffsets": "8657", "endColumns": "156", "endOffsets": "8809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3399,3470,3534,4277,4954,5028,5134,5241,5296,5385,5471,5540,5627,5729,5785,5875,5924,6001,6108,6173,6244,6311,6377,6426,6506,6598,6673,6720,6774,6848,6906,6985,7164,7319,7452,7519,7613,7692,7789,7872,7953,8029,8118,8200,8308,8395,8489,8545,8682,8732,8787,8876,8943,9012,9082,9151,9239,9310,9361", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3394,3465,3529,4272,4949,5023,5129,5236,5291,5380,5466,5535,5622,5724,5780,5870,5919,5996,6103,6168,6239,6306,6372,6421,6501,6593,6668,6715,6769,6843,6901,6980,7159,7314,7447,7514,7608,7687,7784,7867,7948,8024,8113,8195,8303,8390,8484,8540,8677,8727,8782,8871,8938,9007,9077,9146,9234,9305,9356,9430"}, "to": {"startLines": "207,208,209,210,211,212,213,217,218,219,220,223,225,226,228,233,237,252,255,256,257,259,260,261,263,267,268,269,270,271,272,273,274,275,276,278,281,282,288,289,290,301,302,303,304,305,306,307,308,309,310,311,312,319,320,321,324,325,326,327,331,336,337,338,339,340,345,346,347,348,349,350,354,355,365,366,368,369,373,374,376,381,388,389,460,461,462,464,469,482,483,484,485,486,487,488,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19613,19682,19760,19830,19912,20008,20073,20388,20443,20514,20603,20820,21005,21110,21265,21621,21920,22984,23251,23353,23427,23594,23687,23777,23952,24262,24361,24442,24548,24622,24715,24820,24914,25008,25109,25251,25508,25624,26460,26531,26595,28317,28994,29068,29174,29281,29336,29425,29511,29580,29667,29769,29825,30449,30498,30575,30850,30915,30986,31053,31616,31985,32065,32157,32232,32279,32587,32661,32719,32798,32977,33132,33464,33531,34521,34600,34779,34862,35226,35302,35476,36137,36701,36788,44546,44602,44739,44900,45492,47759,47826,47895,47965,48034,48122,48193,49652", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "19677,19755,19825,19907,20003,20068,20139,20438,20509,20598,20672,20920,21105,21193,21342,21701,22005,23081,23348,23422,23521,23682,23772,23887,24032,24356,24437,24543,24617,24710,24815,24909,25003,25104,25170,25317,25619,25728,26526,26590,27333,28989,29063,29169,29276,29331,29420,29506,29575,29662,29764,29820,29910,30493,30570,30677,30910,30981,31048,31114,31660,32060,32152,32227,32274,32328,32656,32714,32793,32972,33127,33260,33526,33620,34595,34692,34857,34938,35297,35386,35553,36240,36783,36877,44597,44734,44784,44950,45576,47821,47890,47960,48029,48117,48188,48239,49721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19067,19153,19256,19323,19390,19454,19541", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "19148,19251,19318,19385,19449,19536,19608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "222,232,234,235,236,240,248,251,254,258,262,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20740,21561,21706,21770,21855,22146,22712,22907,23174,23526,23892,24188", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "20815,21616,21765,21850,21915,22213,22765,22979,23246,23589,23947,24257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3148,3258", "endColumns": "109,118", "endOffsets": "3253,3372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,359,461,673,719,786,885,960,1228,1288,1380,1459,1513,1577,1653,1745,2082,2155,2223,2276,2329,2408,2523,2634,2701,2780,2862,2946,3031,3150,3444,3533,3610,3692,3752,3817,3877,3949,4031,4138,4237,4338,4467,4565,4639,4718,4814,5001,5212,5343,5475,5538,6245,6349,6413", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "179,354,456,668,714,781,880,955,1223,1283,1375,1454,1508,1572,1648,1740,2077,2150,2218,2271,2324,2403,2518,2629,2696,2775,2857,2941,3026,3145,3439,3528,3605,3687,3747,3812,3872,3944,4026,4133,4232,4333,4462,4560,4634,4713,4809,4996,5207,5338,5470,5533,6240,6344,6408,6474"}, "to": {"startLines": "283,284,285,287,291,292,293,294,295,296,297,313,316,318,322,323,328,334,335,343,353,357,358,359,360,361,367,370,375,377,378,379,380,383,385,386,390,394,395,397,399,411,436,437,438,439,440,458,465,466,467,468,470,471,472,489", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25733,25862,26037,26248,27338,27384,27451,27550,27625,27893,27953,29915,30189,30385,30682,30758,31119,31844,31917,32470,33411,33717,33796,33911,34022,34089,34697,34943,35391,35558,35677,35971,36060,36302,36503,36563,36882,37483,37555,37728,37933,39701,42172,42301,42399,42473,42552,44267,44955,45166,45297,45429,45581,46288,46392,48244", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "25857,26032,26134,26455,27379,27446,27545,27620,27888,27948,28040,29989,30238,30444,30753,30845,31451,31912,31980,32518,33459,33791,33906,34017,34084,34163,34774,35022,35471,35672,35966,36055,36132,36379,36558,36623,36937,37550,37632,37830,38027,39797,42296,42394,42468,42547,42643,44449,45161,45292,45424,45487,46283,46387,46451,48305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "98,109,110,111", "startColumns": "4,4,4,4", "startOffsets": "10112,11098,11203,11315", "endColumns": "107,104,111,104", "endOffsets": "10215,11198,11310,11415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "221,224,227,229,230,231,238,239,241,242,243,244,245,246,247,249,250,253,264,265,277,279,280,314,315,329,341,342,344,351,352,362,363,371,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20677,20925,21198,21347,21431,21500,22010,22082,22218,22283,22369,22439,22511,22574,22648,22770,22843,23086,24037,24102,25175,25322,25412,29994,30108,31456,32333,32384,32523,33265,33337,34168,34284,35027,35128", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "20735,21000,21260,21426,21495,21556,22077,22141,22278,22364,22434,22506,22569,22643,22707,22838,22902,23169,24097,24183,25246,25407,25503,30103,30184,31502,32379,32465,32582,33332,33406,34279,34373,35123,35221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "78,79,80,81,82,83,84,85,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7658,7765,7931,8057,8167,8309,8438,8553,8814,8995,9102,9265,9391,9558,9716,9785,9845", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "7760,7926,8052,8162,8304,8433,8548,8652,8990,9097,9260,9386,9553,9711,9780,9840,9926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "54,102,107", "startColumns": "4,4,4", "startOffsets": "5035,10515,10921", "endColumns": "76,80,77", "endOffsets": "5107,10591,10994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,18563", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,18640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,257,377,508,650,789,921,1064,1163,1304,1461", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "157,252,372,503,645,784,916,1059,1158,1299,1456,1581"}, "to": {"startLines": "96,100,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9931,10318,11420,11540,11671,11813,11952,12084,12227,12326,12467,12624", "endColumns": "106,94,119,130,141,138,131,142,98,140,156,124", "endOffsets": "10033,10408,11535,11666,11808,11947,12079,12222,12321,12462,12619,12744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,104,105,108,123,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,190,195,196,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3377,3455,3533,3617,3715,4533,4630,4767,10688,10763,10999,12810,13059,13122,13240,13301,13366,13423,13493,13554,13608,13724,13781,13843,13897,13971,14099,14187,14274,14377,14469,14555,14692,14776,14861,14995,15086,15162,15216,15267,15333,15405,15483,15554,15636,15716,15792,15869,15946,16053,16142,16215,16305,16400,16474,16555,16648,16703,16784,16850,16936,17021,17083,17147,17210,17282,17380,17479,17574,17666,17724,18167,18645,18739,18887", "endLines": "7,37,38,39,40,41,49,50,51,104,105,108,123,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,190,195,196,198", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3450,3528,3612,3710,3801,4625,4762,4854,10758,10824,11093,12882,13117,13235,13296,13361,13418,13488,13549,13603,13719,13776,13838,13892,13966,14094,14182,14269,14372,14464,14550,14687,14771,14856,14990,15081,15157,15211,15262,15328,15400,15478,15549,15631,15711,15787,15864,15941,16048,16137,16210,16300,16395,16469,16550,16643,16698,16779,16845,16931,17016,17078,17142,17205,17277,17375,17474,17569,17661,17719,17774,18242,18734,18810,18961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "122,529", "startColumns": "4,4", "startOffsets": "12749,52048", "endColumns": "60,78", "endOffsets": "12805,52122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "97,106,187,191,521,527,528", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10038,10829,17949,18247,51263,51888,51970", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "10107,10916,18021,18387,51427,51965,52043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,349,496,609,694,783,859,948,1036,1149,1256,1345,1434,1533,1655,1740,1827,2024,2119,2220,2335,2441", "endColumns": "159,133,146,112,84,88,75,88,87,112,106,88,88,98,121,84,86,196,94,100,114,105,159", "endOffsets": "210,344,491,604,689,778,854,943,1031,1144,1251,1340,1429,1528,1650,1735,1822,2019,2114,2215,2330,2436,2596"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5112,5272,5406,5553,5666,5751,5840,5916,6005,6093,6206,6313,6402,6491,6590,6712,6797,6884,7081,7176,7277,7392,7498", "endColumns": "159,133,146,112,84,88,75,88,87,112,106,88,88,98,121,84,86,196,94,100,114,105,159", "endOffsets": "5267,5401,5548,5661,5746,5835,5911,6000,6088,6201,6308,6397,6486,6585,6707,6792,6879,7076,7171,7272,7387,7493,7653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "33625", "endColumns": "91", "endOffsets": "33712"}}]}]}