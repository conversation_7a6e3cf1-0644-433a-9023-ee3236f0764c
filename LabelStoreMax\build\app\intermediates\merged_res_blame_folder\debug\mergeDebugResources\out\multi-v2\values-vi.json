{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9851,10821,10932,11046", "endColumns": "116,110,113,110", "endOffsets": "9963,10927,11041,11152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,274,364,434,498,567,636,701,772,842,905,965,1033,1106,1179,1240,1322,1386,1471,1550,1625,1716,1823,1909,1960,2007,2090,2157,2228,2297,2391,2476,2577", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "115,193,269,359,429,493,562,631,696,767,837,900,960,1028,1101,1174,1235,1317,1381,1466,1545,1620,1711,1818,1904,1955,2002,2085,2152,2223,2292,2386,2471,2572,2670"}, "to": {"startLines": "212,215,218,220,221,222,229,230,232,233,234,235,236,237,238,240,241,244,255,256,268,270,271,305,306,320,332,333,335,342,343,353,354,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19653,19894,20156,20312,20402,20472,20981,21050,21185,21250,21321,21391,21454,21514,21582,21713,21786,22019,22917,22981,24020,24185,24260,28735,28842,30234,31122,31169,31310,32070,32141,33007,33101,33823,33924", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "19713,19967,20227,20397,20467,20531,21045,21114,21245,21316,21386,21449,21509,21577,21650,21781,21842,22096,22976,23061,24094,24255,24346,28837,28923,30280,31164,31247,31372,32136,32205,33096,33181,33919,34017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,209", "endColumns": "78,74,77", "endOffsets": "129,204,282"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4839,10269,10649", "endColumns": "78,74,77", "endOffsets": "4913,10339,10722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,257,373,501,631,763,877,1012,1118,1256,1402", "endColumns": "106,94,115,127,129,131,113,134,105,137,145,122", "endOffsets": "157,252,368,496,626,758,872,1007,1113,1251,1397,1520"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9672,10074,11157,11273,11401,11531,11663,11777,11912,12018,12156,12302", "endColumns": "106,94,115,127,129,131,113,134,105,137,145,122", "endOffsets": "9774,10164,11268,11396,11526,11658,11772,11907,12013,12151,12297,12420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3240,3319,3396,3474,3554,4364,4463,4577,10436,10499,10727,12486,12738,12797,12883,12945,13006,13064,13128,13189,13243,13360,13417,13477,13531,13606,13733,13817,13897,13992,14076,14154,14284,14368,14446,14580,14671,14752,14803,14854,14920,14988,15064,15135,15215,15294,15369,15442,15518,15624,15713,15790,15881,15975,16049,16119,16212,16261,16342,16408,16493,16579,16641,16705,16768,16839,16938,17043,17141,17246,17301,17746,18233,18315,18466", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3314,3391,3469,3549,3629,4458,4572,4652,10494,10557,10816,12555,12792,12878,12940,13001,13059,13123,13184,13238,13355,13412,13472,13526,13601,13728,13812,13892,13987,14071,14149,14279,14363,14441,14575,14666,14747,14798,14849,14915,14983,15059,15130,15210,15289,15364,15437,15513,15619,15708,15785,15876,15970,16044,16114,16207,16256,16337,16403,16488,16574,16636,16700,16763,16834,16933,17038,17136,17241,17296,17351,17819,18310,18389,18535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "95,104,185,189,512,518,519", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9779,10562,17531,17824,49837,50438,50518", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "9846,10644,17601,17962,50001,50513,50590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "32437", "endColumns": "82", "endOffsets": "32515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,341,481,591,676,762,838,924,1016,1129,1240,1333,1426,1534,1654,1733,1817,2001,2097,2203,2321,2429", "endColumns": "154,130,139,109,84,85,75,85,91,112,110,92,92,107,119,78,83,183,95,105,117,107,145", "endOffsets": "205,336,476,586,671,757,833,919,1011,1124,1235,1328,1421,1529,1649,1728,1812,1996,2092,2198,2316,2424,2570"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4918,5073,5204,5344,5454,5539,5625,5701,5787,5879,5992,6103,6196,6289,6397,6517,6596,6680,6864,6960,7066,7184,7292", "endColumns": "154,130,139,109,84,85,75,85,91,112,110,92,92,107,119,78,83,183,95,105,117,107,145", "endOffsets": "5068,5199,5339,5449,5534,5620,5696,5782,5874,5987,6098,6191,6284,6392,6512,6591,6675,6859,6955,7061,7179,7287,7433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7438,7540,7702,7827,7936,8101,8231,8350,8582,8755,8862,9019,9149,9308,9457,9525,9589", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "7535,7697,7822,7931,8096,8226,8345,8449,8750,8857,9014,9144,9303,9452,9520,9584,9667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3634,3731,3833,3932,4032,4135,4248,18540", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3726,3828,3927,4027,4130,4243,4359,18636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,513,514,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4657,4753,9968,10169,10344,12560,12645,17356,17450,17606,17676,17967,18057,18394,50006,50083,50149", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "4748,4834,10069,10264,10431,12640,12733,17445,17526,17671,17741,18052,18143,18461,50078,50144,50258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "50263,50351", "endColumns": "87,86", "endOffsets": "50346,50433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,304,400,495,583,670,827,923,1029,1126,1257,1320,1433,1503,1680,1882,2024,2111,2218,2309,2409,2582,2693,2801,3061,3182,3319,3518,3727,3828,3941,4138,4236,4295,4360,4446,4539,4642,4714,4820,4906,5025,5233,5298,5363,5445,5561,5643,5733,5798,5874,5950,6024,6156,6244,6351,6448,6516,6591,6656,6749,6806,6915,7023,7147,7272,7333,7431,7542,7669,7744,7837,7942,8006,8063,8155,8258,8437,8693,8975,9084,9156,9243,9318,9454,9578,9674,9745,9818,9899,9979,10078,10200,10330,10403,10469,10523,10695,10766,10825,10906,10986,11064,11148,11274,11402,11512,11599,11673,11769,11836", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "127,217,299,395,490,578,665,822,918,1024,1121,1252,1315,1428,1498,1675,1877,2019,2106,2213,2304,2404,2577,2688,2796,3056,3177,3314,3513,3722,3823,3936,4133,4231,4290,4355,4441,4534,4637,4709,4815,4901,5020,5228,5293,5358,5440,5556,5638,5728,5793,5869,5945,6019,6151,6239,6346,6443,6511,6586,6651,6744,6801,6910,7018,7142,7267,7328,7426,7537,7664,7739,7832,7937,8001,8058,8150,8253,8432,8688,8970,9079,9151,9238,9313,9449,9573,9669,9740,9813,9894,9974,10073,10195,10325,10398,10464,10518,10690,10761,10820,10901,10981,11059,11143,11269,11397,11507,11594,11668,11764,11831,11997"}, "to": {"startLines": "205,206,207,277,289,290,291,308,321,323,324,355,373,375,378,382,383,384,387,389,391,392,393,394,395,396,397,398,399,400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,450,454,464,465,466,467,468,469,470,471,472,481,482,483,484,485,486,487,488,489,490,491,492,493,494,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19104,19181,19271,24930,26778,26873,26961,28981,30285,30427,30533,33186,35080,35204,35449,35759,35936,36138,36420,36605,36812,36903,37003,37176,37287,37395,37655,37776,37913,38112,38321,38516,38629,38826,38924,38983,39048,39134,39227,39330,39402,39508,39594,39713,39921,39986,40051,40133,40249,40331,40421,40486,40562,40638,40712,41279,41367,41474,41571,41639,41714,41779,41872,41929,42038,42146,42270,42395,42456,42554,42665,42792,43095,43398,45142,45206,45263,45355,45458,45637,45893,46175,46284,46905,46992,47067,47203,47327,47423,47494,47567,47648,47728,47827,47949,48079,48152,48304,48358,48530,48601,48660,48741,48821,48899,48983,49109,49237,49347,49434,49508,49604,49671", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "19176,19266,19348,25021,26868,26956,27043,29133,30376,30528,30625,33312,35138,35312,35514,35931,36133,36275,36502,36707,36898,36998,37171,37282,37390,37650,37771,37908,38107,38316,38417,38624,38821,38919,38978,39043,39129,39222,39325,39397,39503,39589,39708,39916,39981,40046,40128,40244,40326,40416,40481,40557,40633,40707,40839,41362,41469,41566,41634,41709,41774,41867,41924,42033,42141,42265,42390,42451,42549,42660,42787,42862,43183,43498,45201,45258,45350,45453,45632,45888,46170,46279,46351,46987,47062,47198,47322,47418,47489,47562,47643,47723,47822,47944,48074,48147,48213,48353,48525,48596,48655,48736,48816,48894,48978,49104,49232,49342,49429,49503,49599,49666,49832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,18148", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,18228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8454", "endColumns": "127", "endOffsets": "8577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,192,254,324,403,459,518,570,655,736,818,916,1014,1100,1180,1264,1345,1441,1534,1604,1694,1777,1864,1971,2056,2141,2222,2323,2406,2499,2588,2673,2757,2865,2945,3031,3129,3220,3295,3356,4064,4748,4823,4938,5043,5098,5189,5278,5345,5442,5539,5595,5681,5727,5811,5913,5977,6051,6118,6184,6230,6314,6412,6493,6538,6582,6645,6707,6769,6967,7147,7275,7344,7444,7516,7611,7695,7776,7859,7955,8027,8146,8225,8321,8376,8485,8531,8584,8654,8715,8783,8857,8936,9028,9094,9145", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "114,187,249,319,398,454,513,565,650,731,813,911,1009,1095,1175,1259,1340,1436,1529,1599,1689,1772,1859,1966,2051,2136,2217,2318,2401,2494,2583,2668,2752,2860,2940,3026,3124,3215,3290,3351,4059,4743,4818,4933,5038,5093,5184,5273,5340,5437,5534,5590,5676,5722,5806,5908,5972,6046,6113,6179,6225,6309,6407,6488,6533,6577,6640,6702,6764,6962,7142,7270,7339,7439,7511,7606,7690,7771,7854,7950,8022,8141,8220,8316,8371,8480,8526,8579,8649,8710,8778,8852,8931,9023,9089,9140,9226"}, "to": {"startLines": "198,199,200,201,202,203,204,208,209,210,211,214,216,217,219,224,228,243,246,247,248,250,251,252,254,258,259,260,261,262,263,264,265,266,267,269,272,273,279,280,281,292,293,294,295,296,297,298,299,300,301,302,303,310,311,312,315,316,317,318,322,327,328,329,330,331,336,337,338,339,340,341,345,346,356,357,359,360,364,365,367,372,379,380,451,452,453,455,460,473,474,475,476,477,478,479,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18641,18705,18778,18840,18910,18989,19045,19353,19405,19490,19571,19796,19972,20070,20232,20600,20900,21923,22174,22267,22337,22490,22573,22660,22832,23131,23216,23297,23398,23481,23574,23663,23748,23832,23940,24099,24351,24449,25266,25341,25402,27048,27732,27807,27922,28027,28082,28173,28262,28329,28426,28523,28579,29202,29248,29332,29614,29678,29752,29819,30381,30770,30854,30952,31033,31078,31377,31440,31502,31564,31762,31942,32268,32337,33317,33389,33570,33654,34022,34105,34274,34961,35519,35598,43188,43243,43352,43503,44082,46356,46417,46485,46559,46638,46730,46796,48218", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "18700,18773,18835,18905,18984,19040,19099,19400,19485,19566,19648,19889,20065,20151,20307,20679,20976,22014,22262,22332,22422,22568,22655,22762,22912,23211,23292,23393,23476,23569,23658,23743,23827,23935,24015,24180,24444,24535,25336,25397,26105,27727,27802,27917,28022,28077,28168,28257,28324,28421,28518,28574,28660,29243,29327,29429,29673,29747,29814,29880,30422,30849,30947,31028,31073,31117,31435,31497,31559,31757,31937,32065,32332,32432,33384,33479,33649,33730,34100,34196,34341,35075,35593,35689,43238,43347,43393,43551,44147,46412,46480,46554,46633,46725,46791,46842,48299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "120,520", "startColumns": "4,4", "startOffsets": "12425,50595", "endColumns": "60,79", "endOffsets": "12481,50670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,197,263,352,413,479,537,613,686,749,814", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "128,192,258,347,408,474,532,608,681,744,809,874"}, "to": {"startLines": "213,223,225,226,227,231,239,242,245,249,253,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19718,20536,20684,20750,20839,21119,21655,21847,22101,22427,22767,23066", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "19791,20595,20745,20834,20895,21180,21708,21918,22169,22485,22827,23126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3008,3120", "endColumns": "111,119", "endOffsets": "3115,3235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,341,445,685,734,802,902,981,1207,1268,1353,1423,1476,1540,1622,1720,2069,2143,2209,2267,2325,2409,2545,2662,2724,2812,2898,2986,3059,3161,3514,3594,3674,3735,3800,3867,3932,3998,4072,4170,4270,4364,4466,4562,4634,4713,4799,5027,5258,5366,5499,5553,6376,6481,6543", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "166,336,440,680,729,797,897,976,1202,1263,1348,1418,1471,1535,1617,1715,2064,2138,2204,2262,2320,2404,2540,2657,2719,2807,2893,2981,3054,3156,3509,3589,3669,3730,3795,3862,3927,3993,4067,4165,4265,4359,4461,4557,4629,4708,4794,5022,5253,5361,5494,5548,6371,6476,6538,6596"}, "to": {"startLines": "274,275,276,278,282,283,284,285,286,287,288,304,307,309,313,314,319,325,326,334,344,348,349,350,351,352,358,361,366,368,369,370,371,374,376,377,381,385,386,388,390,402,427,428,429,430,431,449,456,457,458,459,461,462,463,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24540,24656,24826,25026,26110,26159,26227,26327,26406,26632,26693,28665,28928,29138,29434,29516,29885,30630,30704,31252,32210,32520,32604,32740,32857,32919,33484,33735,34201,34346,34448,34801,34881,35143,35317,35382,35694,36280,36346,36507,36712,38422,40844,40946,41042,41114,41193,42867,43556,43787,43895,44028,44152,44975,45080,46847", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "24651,24821,24925,25261,26154,26222,26322,26401,26627,26688,26773,28730,28976,29197,29511,29609,30229,30699,30765,31305,32263,32599,32735,32852,32914,33002,33565,33818,34269,34443,34796,34876,34956,35199,35377,35444,35754,36341,36415,36600,36807,38511,40941,41037,41109,41188,41274,43090,43782,43890,44023,44077,44970,45075,45137,46900"}}]}]}