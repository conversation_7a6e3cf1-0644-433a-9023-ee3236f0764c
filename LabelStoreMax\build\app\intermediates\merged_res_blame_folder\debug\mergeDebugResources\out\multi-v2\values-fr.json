{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4798,4897,10398,10590,10773,13141,13220,18083,18175,18340,18411,18710,18791,19127,52646,52724,52793", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4892,4980,10493,10685,10855,13215,13307,18170,18257,18406,18474,18786,18871,19198,52719,52788,52910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,388,482,566,649,801,885,1001,1105,1238,1304,1428,1493,1664,1863,2036,2126,2236,2338,2440,2633,2755,2857,3120,3231,3373,3604,3859,3959,4096,4306,4405,4466,4532,4617,4719,4822,4897,4997,5089,5194,5447,5517,5589,5670,5804,5879,5972,6039,6123,6194,6263,6383,6480,6594,6697,6785,6884,6955,7050,7115,7230,7347,7454,7596,7661,7765,7877,8028,8100,8196,8310,8372,8430,8509,8633,8813,9124,9458,9557,9637,9738,9814,9939,10096,10182,10270,10340,10427,10519,10616,10747,10889,10969,11041,11100,11264,11333,11394,11479,11555,11633,11727,11883,12004,12125,12217,12293,12375,12452", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "129,216,299,383,477,561,644,796,880,996,1100,1233,1299,1423,1488,1659,1858,2031,2121,2231,2333,2435,2628,2750,2852,3115,3226,3368,3599,3854,3954,4091,4301,4400,4461,4527,4612,4714,4817,4892,4992,5084,5189,5442,5512,5584,5665,5799,5874,5967,6034,6118,6189,6258,6378,6475,6589,6692,6780,6879,6950,7045,7110,7225,7342,7449,7591,7656,7760,7872,8023,8095,8191,8305,8367,8425,8504,8628,8808,9119,9453,9552,9632,9733,9809,9934,10091,10177,10265,10335,10422,10514,10611,10742,10884,10964,11036,11095,11259,11328,11389,11474,11550,11628,11722,11878,11999,12120,12212,12288,12370,12447,12605"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20443,20522,20609,26438,28376,28470,28554,30643,31930,32062,32178,34961,36902,37045,37293,37627,37798,37997,38333,38549,38764,38866,38968,39161,39283,39385,39648,39759,39901,40132,40387,40586,40723,40933,41032,41093,41159,41244,41346,41449,41524,41624,41716,41821,42074,42144,42216,42297,42431,42506,42599,42666,42750,42821,42890,43475,43572,43686,43789,43877,43976,44047,44142,44207,44322,44439,44546,44688,44753,44857,44969,45120,45390,45737,47521,47583,47641,47720,47844,48024,48335,48669,48768,49425,49526,49602,49727,49884,49970,50058,50128,50215,50307,50404,50535,50677,50757,50908,50967,51131,51200,51261,51346,51422,51500,51594,51750,51871,51992,52084,52160,52242,52319", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "20517,20604,20687,26517,28465,28549,28632,30790,32009,32173,32277,35089,36963,37164,37353,37793,37992,38165,38418,38654,38861,38963,39156,39278,39380,39643,39754,39896,40127,40382,40482,40718,40928,41027,41088,41154,41239,41341,41444,41519,41619,41711,41816,42069,42139,42211,42292,42426,42501,42594,42661,42745,42816,42885,43005,43567,43681,43784,43872,43971,44042,44137,44202,44317,44434,44541,44683,44748,44852,44964,45115,45187,45481,45846,47578,47636,47715,47839,48019,48330,48664,48763,48843,49521,49597,49722,49879,49965,50053,50123,50210,50302,50399,50530,50672,50752,50824,50962,51126,51195,51256,51341,51417,51495,51589,51745,51866,51987,52079,52155,52237,52314,52472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,196,258,340,408,479,537,619,692,759,819", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "131,191,253,335,403,474,532,614,687,754,814,884"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21087,21910,22052,22114,22196,22499,23052,23244,23495,23825,24203,24495", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "21163,21965,22109,22191,22259,22565,23105,23321,23563,23887,24258,24560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10221,10991,18262,18568,52477,53099,53185", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "10286,11083,18335,18705,52641,53180,53260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,228,290,366,451,512,571,649,727,815,901,1004,1107,1195,1278,1360,1450,1554,1649,1719,1811,1900,1997,2122,2204,2296,2373,2472,2548,2648,2748,2843,2928,3043,3118,3203,3325,3440,3523,3588,4351,5022,5101,5215,5326,5381,5492,5604,5672,5773,5873,5929,6017,6067,6150,6268,6344,6421,6488,6554,6602,6690,6793,6872,6920,6969,7055,7113,7177,7378,7574,7731,7799,7888,7974,8082,8190,8300,8383,8479,8559,8673,8775,8878,8932,9077,9129,9211,9280,9350,9426,9496,9570,9668,9748,9799", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "132,223,285,361,446,507,566,644,722,810,896,999,1102,1190,1273,1355,1445,1549,1644,1714,1806,1895,1992,2117,2199,2291,2368,2467,2543,2643,2743,2838,2923,3038,3113,3198,3320,3435,3518,3583,4346,5017,5096,5210,5321,5376,5487,5599,5667,5768,5868,5924,6012,6062,6145,6263,6339,6416,6483,6549,6597,6685,6788,6867,6915,6964,7050,7108,7172,7373,7569,7726,7794,7883,7969,8077,8185,8295,8378,8474,8554,8668,8770,8873,8927,9072,9124,9206,9275,9345,9421,9491,9565,9663,9743,9794,9873"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19927,20009,20100,20162,20238,20323,20384,20692,20770,20848,20936,21168,21352,21455,21610,21970,22264,23326,23568,23663,23733,23892,23981,24078,24263,24565,24657,24734,24833,24909,25009,25109,25204,25289,25404,25560,25833,25955,26732,26815,26880,28637,29308,29387,29501,29612,29667,29778,29890,29958,30059,30159,30215,30859,30909,30992,31272,31348,31425,31492,32014,32416,32504,32607,32686,32734,33042,33128,33186,33250,33451,33647,34005,34073,35094,35180,35376,35484,35906,35989,36156,36788,37358,37460,45486,45540,45685,45851,46478,48848,48918,48994,49064,49138,49236,49316,50829", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "20004,20095,20157,20233,20318,20379,20438,20765,20843,20931,21017,21266,21450,21538,21688,22047,22349,23425,23658,23728,23820,23976,24073,24198,24340,24652,24729,24828,24904,25004,25104,25199,25284,25399,25474,25640,25950,26065,26810,26875,27638,29303,29382,29496,29607,29662,29773,29885,29953,30054,30154,30210,30298,30904,30987,31105,31343,31420,31487,31553,32057,32499,32602,32681,32729,32778,33123,33181,33245,33446,33642,33799,34068,34157,35175,35283,35479,35589,35984,36080,36231,36897,37455,37558,45535,45680,45732,45928,46542,48913,48989,49059,49133,49231,49311,49362,50903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3075,3191", "endColumns": "115,118", "endOffsets": "3186,3305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3758,3856,3958,4057,4159,4263,4367,19284", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3851,3953,4052,4154,4258,4362,4480,19380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,322,423,633,680,752,856,931,1208,1271,1366,1440,1495,1559,1632,1721,2042,2109,2176,2231,2286,2380,2530,2643,2702,2789,2877,2965,3036,3126,3428,3509,3588,3665,3727,3789,3853,3937,4016,4142,4247,4346,4464,4567,4641,4720,4811,5009,5218,5348,5492,5554,6355,6463,6528", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "154,317,418,628,675,747,851,926,1203,1266,1361,1435,1490,1554,1627,1716,2037,2104,2171,2226,2281,2375,2525,2638,2697,2784,2872,2960,3031,3121,3423,3504,3583,3660,3722,3784,3848,3932,4011,4137,4242,4341,4459,4562,4636,4715,4806,5004,5213,5343,5487,5549,6350,6458,6523,6581"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26070,26174,26337,26522,27643,27690,27762,27866,27941,28218,28281,30303,30588,30795,31110,31183,31558,32282,32349,32923,33950,34249,34343,34493,34606,34665,35288,35594,36085,36236,36326,36628,36709,36968,37169,37231,37563,38170,38254,38423,38659,40487,43010,43128,43231,43305,43384,45192,45933,46142,46272,46416,46547,47348,47456,49367", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "26169,26332,26433,26727,27685,27757,27861,27936,28213,28276,28371,30372,30638,30854,31178,31267,31874,32344,32411,32973,34000,34338,34488,34601,34660,34747,35371,35677,36151,36321,36623,36704,36783,37040,37226,37288,37622,38249,38328,38544,38759,40581,43123,43226,43300,43379,43470,45385,46137,46267,46411,46473,47343,47451,47516,49420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "52915,53004", "endColumns": "88,94", "endOffsets": "52999,53094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10291,11262,11364,11483", "endColumns": "106,101,118,104", "endOffsets": "10393,11359,11478,11583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,18876", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,18958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8764", "endColumns": "164", "endOffsets": "8924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,201,268,353,424,485,557,630,692,764,834,902,962,1036,1112,1183,1246,1311,1376,1461,1542,1633,1730,1859,1941,1992,2040,2132,2196,2271,2342,2462,2551,2663", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "115,196,263,348,419,480,552,625,687,759,829,897,957,1031,1107,1178,1241,1306,1371,1456,1537,1628,1725,1854,1936,1987,2035,2127,2191,2266,2337,2457,2546,2658,2770"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21022,21271,21543,21693,21778,21849,22354,22426,22570,22632,22704,22774,22842,22902,22976,23110,23181,23430,24345,24410,25479,25645,25736,30377,30506,31879,32783,32831,32978,33804,33879,34752,34872,35682,35794", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "21082,21347,21605,21773,21844,21905,22421,22494,22627,22699,22769,22837,22897,22971,23047,23176,23239,23490,24405,24490,25555,25731,25828,30501,30583,31925,32826,32918,33037,33874,33945,34867,34956,35789,35901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "12997,53265", "endColumns": "60,78", "endOffsets": "13053,53339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,219,379,530,643,731,818,896,986,1083,1198,1312,1407,1502,1614,1733,1816,1901,2092,2186,2296,2418,2530", "endColumns": "163,159,150,112,87,86,77,89,96,114,113,94,94,111,118,82,84,190,93,109,121,111,165", "endOffsets": "214,374,525,638,726,813,891,981,1078,1193,1307,1402,1497,1609,1728,1811,1896,2087,2181,2291,2413,2525,2691"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5060,5224,5384,5535,5648,5736,5823,5901,5991,6088,6203,6317,6412,6507,6619,6738,6821,6906,7097,7191,7301,7423,7535", "endColumns": "163,159,150,112,87,86,77,89,96,114,113,94,94,111,118,82,84,190,93,109,121,111,165", "endOffsets": "5219,5379,5530,5643,5731,5818,5896,5986,6083,6198,6312,6407,6502,6614,6733,6816,6901,7092,7186,7296,7418,7530,7696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7701,7807,7987,8117,8226,8397,8530,8651,8929,9107,9219,9404,9540,9700,9879,9952,10019", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "7802,7982,8112,8221,8392,8525,8646,8759,9102,9214,9399,9535,9695,9874,9947,10014,10098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "34162", "endColumns": "86", "endOffsets": "34244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,213", "endColumns": "74,82,76", "endOffsets": "125,208,285"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4985,10690,11088", "endColumns": "74,82,76", "endOffsets": "5055,10768,11160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3310,3390,3471,3554,3663,4485,4583,4713,10860,10925,11165,13058,13312,13378,13480,13545,13620,13676,13755,13815,13869,13991,14050,14112,14166,14248,14383,14475,14550,14645,14726,14810,14954,15033,15114,15255,15348,15427,15482,15533,15599,15679,15760,15831,15911,15984,16062,16135,16207,16319,16412,16484,16576,16668,16742,16826,16918,16975,17059,17125,17208,17295,17357,17421,17484,17562,17664,17768,17865,17969,18028,18479,18963,19050,19203", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3385,3466,3549,3658,3753,4578,4708,4793,10920,10986,11257,13136,13373,13475,13540,13615,13671,13750,13810,13864,13986,14045,14107,14161,14243,14378,14470,14545,14640,14721,14805,14949,15028,15109,15250,15343,15422,15477,15528,15594,15674,15755,15826,15906,15979,16057,16130,16202,16314,16407,16479,16571,16663,16737,16821,16913,16970,17054,17120,17203,17290,17352,17416,17479,17557,17659,17763,17860,17964,18023,18078,18563,19045,19122,19279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,454,529", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "140,246,313,381,449,524,592"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19385,19475,19581,19648,19716,19784,19859", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "19470,19576,19643,19711,19779,19854,19922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,265,388,537,697,839,970,1141,1239,1382,1543", "endColumns": "117,91,122,148,159,141,130,170,97,142,160,130", "endOffsets": "168,260,383,532,692,834,965,1136,1234,1377,1538,1669"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10103,10498,11588,11711,11860,12020,12162,12293,12464,12562,12705,12866", "endColumns": "117,91,122,148,159,141,130,170,97,142,160,130", "endOffsets": "10216,10585,11706,11855,12015,12157,12288,12459,12557,12700,12861,12992"}}]}]}