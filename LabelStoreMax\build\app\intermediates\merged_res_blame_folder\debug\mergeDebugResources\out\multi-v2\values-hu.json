{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,353,506,617,701,789,865,954,1044,1159,1272,1369,1466,1572,1704,1786,1872,2066,2162,2268,2383,2499", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "219,348,501,612,696,784,860,949,1039,1154,1267,1364,1461,1567,1699,1781,1867,2061,2157,2263,2378,2494,2653"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4989,5158,5287,5440,5551,5635,5723,5799,5888,5978,6093,6206,6303,6400,6506,6638,6720,6806,7000,7096,7202,7317,7433", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "5153,5282,5435,5546,5630,5718,5794,5883,5973,6088,6201,6298,6395,6501,6633,6715,6801,6995,7091,7197,7312,7428,7587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "32818", "endColumns": "89", "endOffsets": "32903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10202,11143,11244,11359", "endColumns": "95,100,114,103", "endOffsets": "10293,11239,11354,11458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,374,477,688,735,801,890,960,1219,1283,1371,1437,1491,1555,1631,1723,2039,2118,2185,2238,2291,2369,2492,2605,2662,2738,2815,2899,2975,3083,3392,3470,3547,3618,3678,3745,3805,3880,3962,4059,4158,4249,4343,4441,4516,4595,4683,4882,5089,5202,5343,5406,6105,6212,6276", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "184,369,472,683,730,796,885,955,1214,1278,1366,1432,1486,1550,1626,1718,2034,2113,2180,2233,2286,2364,2487,2600,2657,2733,2810,2894,2970,3078,3387,3465,3542,3613,3673,3740,3800,3875,3957,4054,4153,4244,4338,4436,4511,4590,4678,4877,5084,5197,5338,5401,6100,6207,6271,6333"}, "to": {"startLines": "274,275,276,278,282,283,284,285,286,287,288,304,307,309,313,314,319,325,326,334,344,348,349,350,351,352,358,361,366,368,369,370,371,374,376,377,381,385,386,388,390,402,427,428,429,430,431,449,456,457,458,459,461,462,463,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25087,25221,25406,25611,26659,26706,26772,26861,26931,27190,27254,29158,29412,29610,29920,29996,30366,31064,31143,31678,32613,32908,32986,33109,33222,33279,33816,34059,34496,34644,34752,35061,35139,35389,35584,35644,35954,36537,36612,36786,36981,38645,41047,41141,41239,41314,41393,43057,43756,43963,44076,44217,44353,45052,45159,46952", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "25216,25401,25504,25817,26701,26767,26856,26926,27185,27249,27337,29219,29461,29669,29991,30083,30677,31138,31205,31726,32661,32981,33104,33217,33274,33350,33888,34138,34567,34747,35056,35134,35211,35455,35639,35706,36009,36607,36689,36878,37075,38731,41136,41234,41309,41388,41476,43251,43958,44071,44212,44275,45047,45154,45218,47009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,215", "endColumns": "79,79,75", "endOffsets": "130,210,286"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4909,10586,10968", "endColumns": "79,79,75", "endOffsets": "4984,10661,11039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,189,258,345,420,481,547,613,678,751,818,887,950,1024,1088,1157,1221,1300,1365,1448,1526,1608,1697,1805,1885,1937,1985,2061,2125,2196,2269,2361,2440,2538", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "111,184,253,340,415,476,542,608,673,746,813,882,945,1019,1083,1152,1216,1295,1360,1443,1521,1603,1692,1800,1880,1932,1980,2056,2120,2191,2264,2356,2435,2533,2626"}, "to": {"startLines": "212,215,218,220,221,222,229,230,232,233,234,235,236,237,238,240,241,244,255,256,268,270,271,305,306,320,332,333,335,342,343,353,354,362,363", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20170,20403,20660,20814,20901,20976,21480,21546,21682,21747,21820,21887,21956,22019,22093,22215,22284,22520,23439,23504,24534,24690,24772,29224,29332,30682,31554,31602,31731,32469,32540,33355,33447,34143,34241", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "20226,20471,20724,20896,20971,21032,21541,21607,21742,21815,21882,21951,22014,22088,22152,22279,22343,22594,23499,23582,24607,24767,24856,29327,29407,30729,31597,31673,31790,32535,32608,33442,33521,34236,34329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7592,7703,7887,8025,8134,8302,8440,8562,8849,9019,9127,9312,9449,9621,9793,9864,9932", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "7698,7882,8020,8129,8297,8435,8557,8667,9014,9122,9307,9444,9616,9788,9859,9927,10015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,258,385,539,683,810,939,1098,1201,1342,1493", "endColumns": "110,91,126,153,143,126,128,158,102,140,150,129", "endOffsets": "161,253,380,534,678,805,934,1093,1196,1337,1488,1618"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10020,10395,11463,11590,11744,11888,12015,12144,12303,12406,12547,12698", "endColumns": "110,91,126,153,143,126,128,158,102,140,150,129", "endOffsets": "10126,10482,11585,11739,11883,12010,12139,12298,12401,12542,12693,12823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3298,3379,3455,3532,3622,4424,4523,4643,10753,10816,11044,12889,13142,13201,13311,13373,13442,13500,13572,13633,13688,13791,13848,13908,13963,14044,14164,14247,14325,14421,14507,14595,14730,14813,14893,15033,15127,15209,15262,15313,15379,15455,15537,15608,15692,15769,15844,15923,16000,16105,16201,16278,16370,16467,16541,16626,16723,16775,16858,16925,17013,17100,17162,17226,17289,17355,17453,17559,17653,17760,17817,18258,18735,18820,18971", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3374,3450,3527,3617,3697,4518,4638,4721,10811,10875,11138,12959,13196,13306,13368,13437,13495,13567,13628,13683,13786,13843,13903,13958,14039,14159,14242,14320,14416,14502,14590,14725,14808,14888,15028,15122,15204,15257,15308,15374,15450,15532,15603,15687,15764,15839,15918,15995,16100,16196,16273,16365,16462,16536,16621,16718,16770,16853,16920,17008,17095,17157,17221,17284,17350,17448,17554,17648,17755,17812,17867,18338,18815,18892,19039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "95,104,185,189,512,518,519", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10131,10880,18048,18343,49894,50515,50594", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "10197,10963,18125,18475,50058,50589,50665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "50329,50418", "endColumns": "88,96", "endOffsets": "50413,50510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8672", "endColumns": "176", "endOffsets": "8844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3702,3799,3901,4003,4104,4207,4314,19044", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3794,3896,3998,4099,4202,4309,4419,19140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,513,514,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4726,4821,10298,10487,10666,12964,13046,17872,17961,18130,18194,18480,18563,18897,50063,50142,50208", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "4816,4904,10390,10581,10748,13041,13137,17956,18043,18189,18253,18558,18646,18966,50137,50203,50324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3067,3177", "endColumns": "109,120", "endOffsets": "3172,3293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,338,419,481,549,603,678,763,843,940,1039,1124,1209,1293,1380,1478,1576,1647,1742,1834,1920,2030,2115,2210,2288,2390,2464,2555,2647,2734,2818,2923,2991,3069,3185,3295,3374,3437,4132,4807,4880,4975,5080,5135,5223,5312,5376,5465,5561,5617,5705,5753,5837,5951,6026,6096,6163,6229,6278,6356,6450,6525,6570,6622,6688,6746,6808,6995,7166,7296,7361,7448,7518,7610,7690,7776,7847,7938,8010,8119,8206,8297,8352,8483,8537,8595,8668,8738,8809,8874,8943,9033,9104,9156", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "118,195,261,333,414,476,544,598,673,758,838,935,1034,1119,1204,1288,1375,1473,1571,1642,1737,1829,1915,2025,2110,2205,2283,2385,2459,2550,2642,2729,2813,2918,2986,3064,3180,3290,3369,3432,4127,4802,4875,4970,5075,5130,5218,5307,5371,5460,5556,5612,5700,5748,5832,5946,6021,6091,6158,6224,6273,6351,6445,6520,6565,6617,6683,6741,6803,6990,7161,7291,7356,7443,7513,7605,7685,7771,7842,7933,8005,8114,8201,8292,8347,8478,8532,8590,8663,8733,8804,8869,8938,9028,9099,9151,9225"}, "to": {"startLines": "198,199,200,201,202,203,204,208,209,210,211,214,216,217,219,224,228,243,246,247,248,250,251,252,254,258,259,260,261,262,263,264,265,266,267,269,272,273,279,280,281,292,293,294,295,296,297,298,299,300,301,302,303,310,311,312,315,316,317,318,322,327,328,329,330,331,336,337,338,339,340,341,345,346,356,357,359,360,364,365,367,372,379,380,451,452,453,455,460,473,474,475,476,477,478,479,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19145,19213,19290,19356,19428,19509,19571,19876,19930,20005,20090,20306,20476,20575,20729,21097,21393,22422,22673,22771,22842,23005,23097,23183,23354,23658,23753,23831,23933,24007,24098,24190,24277,24361,24466,24612,24861,24977,25822,25901,25964,27585,28260,28333,28428,28533,28588,28676,28765,28829,28918,29014,29070,29674,29722,29806,30088,30163,30233,30300,30836,31210,31288,31382,31457,31502,31795,31861,31919,31981,32168,32339,32666,32731,33654,33724,33893,33973,34334,34405,34572,35216,35776,35863,43351,43406,43537,43698,44280,46464,46534,46605,46670,46739,46829,46900,48329", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "19208,19285,19351,19423,19504,19566,19634,19925,20000,20085,20165,20398,20570,20655,20809,21176,21475,22515,22766,22837,22932,23092,23178,23288,23434,23748,23826,23928,24002,24093,24185,24272,24356,24461,24529,24685,24972,25082,25896,25959,26654,28255,28328,28423,28528,28583,28671,28760,28824,28913,29009,29065,29153,29717,29801,29915,30158,30228,30295,30361,30880,31283,31377,31452,31497,31549,31856,31914,31976,32163,32334,32464,32726,32813,33719,33811,33968,34054,34400,34491,34639,35320,35858,35949,43401,43532,43586,43751,44348,46529,46600,46665,46734,46824,46895,46947,48398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,214,292,394,480,559,637,781,883,978,1062,1190,1254,1378,1443,1623,1833,1966,2058,2156,2247,2343,2518,2618,2720,2966,3065,3189,3404,3625,3721,3835,4027,4121,4183,4251,4333,4424,4516,4592,4687,4776,4876,5105,5179,5246,5317,5428,5518,5606,5683,5769,5843,5925,6032,6121,6231,6329,6404,6491,6565,6654,6714,6812,6912,7015,7151,7213,7307,7408,7534,7608,7703,7810,7874,7939,8024,8140,8328,8598,8886,8982,9051,9141,9215,9335,9454,9545,9628,9699,9782,9865,9955,10068,10224,10295,10366,10435,10607,10675,10741,10827,10899,10977,11053,11174,11283,11380,11468,11548,11636,11708", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "124,209,287,389,475,554,632,776,878,973,1057,1185,1249,1373,1438,1618,1828,1961,2053,2151,2242,2338,2513,2613,2715,2961,3060,3184,3399,3620,3716,3830,4022,4116,4178,4246,4328,4419,4511,4587,4682,4771,4871,5100,5174,5241,5312,5423,5513,5601,5678,5764,5838,5920,6027,6116,6226,6324,6399,6486,6560,6649,6709,6807,6907,7010,7146,7208,7302,7403,7529,7603,7698,7805,7869,7934,8019,8135,8323,8593,8881,8977,9046,9136,9210,9330,9449,9540,9623,9694,9777,9860,9950,10063,10219,10290,10361,10430,10602,10670,10736,10822,10894,10972,11048,11169,11278,11375,11463,11543,11631,11703,11852"}, "to": {"startLines": "205,206,207,277,289,290,291,308,321,323,324,355,373,375,378,382,383,384,387,389,391,392,393,394,395,396,397,398,399,400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,450,454,464,465,466,467,468,469,470,471,472,481,482,483,484,485,486,487,488,489,490,491,492,493,494,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19639,19713,19798,25509,27342,27428,27507,29466,30734,30885,30980,33526,35325,35460,35711,36014,36194,36404,36694,36883,37080,37171,37267,37442,37542,37644,37890,37989,38113,38328,38549,38736,38850,39042,39136,39198,39266,39348,39439,39531,39607,39702,39791,39891,40120,40194,40261,40332,40443,40533,40621,40698,40784,40858,40940,41481,41570,41680,41778,41853,41940,42014,42103,42163,42261,42361,42464,42600,42662,42756,42857,42983,43256,43591,45223,45287,45352,45437,45553,45741,46011,46299,46395,47014,47104,47178,47298,47417,47508,47591,47662,47745,47828,47918,48031,48187,48258,48403,48472,48644,48712,48778,48864,48936,49014,49090,49211,49320,49417,49505,49585,49673,49745", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "19708,19793,19871,25606,27423,27502,27580,29605,30831,30975,31059,33649,35384,35579,35771,36189,36399,36532,36781,36976,37166,37262,37437,37537,37639,37885,37984,38108,38323,38544,38640,38845,39037,39131,39193,39261,39343,39434,39526,39602,39697,39786,39886,40115,40189,40256,40327,40438,40528,40616,40693,40779,40853,40935,41042,41565,41675,41773,41848,41935,42009,42098,42158,42256,42356,42459,42595,42657,42751,42852,42978,43052,43346,43693,45282,45347,45432,45548,45736,46006,46294,46390,46459,47099,47173,47293,47412,47503,47586,47657,47740,47823,47913,48026,48182,48253,48324,48467,48639,48707,48773,48859,48931,49009,49085,49206,49315,49412,49500,49580,49668,49740,49889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,190,254,339,402,472,530,604,678,746,807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "125,185,249,334,397,467,525,599,673,741,802,873"}, "to": {"startLines": "213,223,225,226,227,231,239,242,245,249,253,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20231,21037,21181,21245,21330,21612,22157,22348,22599,22937,23293,23587", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "20301,21092,21240,21325,21388,21677,22210,22417,22668,23000,23349,23653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,18651", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,18730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,76", "endOffsets": "258,335"}, "to": {"startLines": "120,520", "startColumns": "4,4", "startOffsets": "12828,50670", "endColumns": "60,80", "endOffsets": "12884,50746"}}]}]}