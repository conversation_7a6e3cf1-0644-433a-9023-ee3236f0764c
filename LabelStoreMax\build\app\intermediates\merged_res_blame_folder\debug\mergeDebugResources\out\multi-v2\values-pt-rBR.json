{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16411,16498,16606,16673,16741,16807,16881", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "16493,16601,16668,16736,16802,16876,16945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,15909", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,15990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "97,503", "startColumns": "4,4", "startOffsets": "10029,48188", "endColumns": "60,79", "endOffsets": "10085,48263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "196,199,202,204,205,206,213,214,216,217,218,219,220,221,222,224,225,228,239,240,252,254,255,289,290,304,316,317,319,326,327,336,337,345,346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17982,18232,18502,18648,18734,18805,19300,19373,19513,19575,19643,19713,19773,19834,19908,20031,20100,20325,21233,21298,22317,22474,22553,26948,27054,28398,29274,29322,29451,30197,30259,31013,31100,31806,31902", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "18043,18309,18565,18729,18800,18861,19368,19435,19570,19638,19708,19768,19829,19903,19967,20095,20158,20398,21293,21381,22392,22548,22634,27049,27140,28443,29317,29392,29510,30254,30323,31095,31184,31897,31978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,79,80,83,98,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,165,170,171,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3292,3373,3451,3535,3630,4457,4558,4678,8025,8085,8310,10090,10349,10409,10499,10563,10634,10697,10772,10836,10890,11017,11075,11137,11191,11270,11411,11498,11574,11669,11750,11832,11971,12054,12138,12277,12364,12444,12500,12551,12617,12691,12771,12842,12925,12998,13075,13144,13218,13320,13408,13485,13578,13674,13748,13828,13925,13977,14061,14127,14214,14302,14364,14428,14491,14559,14668,14779,14883,14993,15053,15506,15995,16078,16231", "endLines": "5,35,36,37,38,39,47,48,49,79,80,83,98,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,165,170,171,173", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3368,3446,3530,3625,3714,4553,4673,4754,8080,8144,8397,10164,10404,10494,10558,10629,10692,10767,10831,10885,11012,11070,11132,11186,11265,11406,11493,11569,11664,11745,11827,11966,12049,12133,12272,12359,12439,12495,12546,12612,12686,12766,12837,12920,12993,13070,13139,13213,13315,13403,13480,13573,13669,13743,13823,13920,13972,14056,14122,14209,14297,14359,14423,14486,14554,14663,14774,14878,14988,15048,15103,15578,16073,16150,16305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "40,41,42,43,44,45,46,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3719,3816,3918,4017,4117,4227,4337,16310", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3811,3913,4012,4112,4222,4332,4452,16406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3222,3292,3357,4047,4712,4790,4907,5022,5077,5171,5257,5329,5419,5516,5573,5667,5718,5796,5907,5978,6048,6115,6181,6229,6313,6411,6484,6534,6581,6646,6704,6767,6959,7124,7263,7328,7414,7489,7578,7660,7737,7806,7897,7970,8075,8161,8256,8309,8425,8475,8529,8596,8668,8741,8810,8885,8975,9045,9097", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3217,3287,3352,4042,4707,4785,4902,5017,5072,5166,5252,5324,5414,5511,5568,5662,5713,5791,5902,5973,6043,6110,6176,6224,6308,6406,6479,6529,6576,6641,6699,6762,6954,7119,7258,7323,7409,7484,7573,7655,7732,7801,7892,7965,8070,8156,8251,8304,8420,8470,8524,8591,8663,8736,8805,8880,8970,9040,9092,9171"}, "to": {"startLines": "182,183,184,185,186,187,188,192,193,194,195,198,200,201,203,208,212,227,230,231,232,234,235,236,238,242,243,244,245,246,247,248,249,250,251,253,256,257,263,264,265,276,277,278,279,280,281,282,283,284,285,286,287,294,295,296,299,300,301,302,306,311,312,313,314,315,320,321,322,323,324,325,329,330,339,340,342,343,347,348,350,355,362,363,434,435,436,438,443,456,457,458,459,460,461,462,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16950,17024,17107,17169,17243,17326,17389,17700,17754,17830,17903,18130,18314,18416,18570,18927,19216,20234,20478,20573,20645,20805,20893,20981,21151,21458,21550,21629,21728,21802,21888,21975,22058,22141,22244,22397,22639,22741,23515,23585,23650,25256,25921,25999,26116,26231,26286,26380,26466,26538,26628,26725,26782,27404,27455,27533,27804,27875,27945,28012,28543,28922,29006,29104,29177,29227,29515,29580,29638,29701,29893,30058,30385,30450,31313,31388,31558,31640,31983,32052,32209,32841,33367,33453,41021,41074,41190,41341,41903,44014,44086,44159,44228,44303,44393,44463,45835", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "17019,17102,17164,17238,17321,17384,17443,17749,17825,17898,17977,18227,18411,18497,18643,19003,19295,20320,20568,20640,20732,20888,20976,21084,21228,21545,21624,21723,21797,21883,21970,22053,22136,22239,22312,22469,22736,22836,23580,23645,24335,25916,25994,26111,26226,26281,26375,26461,26533,26623,26720,26777,26871,27450,27528,27639,27870,27940,28007,28073,28586,29001,29099,29172,29222,29269,29575,29633,29696,29888,30053,30192,30445,30531,31383,31472,31635,31712,32047,32138,32277,32941,33448,33543,41069,41185,41235,41390,41965,44081,44154,44223,44298,44388,44458,44510,45909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6028", "endColumns": "144", "endOffsets": "6168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,224,307,403,489,572,654,792,887,988,1079,1203,1259,1374,1438,1609,1792,1932,2024,2130,2225,2324,2492,2602,2702,2980,3093,3232,3433,3652,3753,3876,4072,4173,4234,4300,4382,4482,4583,4660,4761,4849,4959,5172,5240,5309,5388,5515,5589,5674,5736,5815,5886,5954,6069,6156,6263,6360,6435,6517,6586,6677,6737,6842,6949,7053,7183,7243,7343,7451,7585,7656,7748,7849,7909,7972,8050,8168,8337,8606,8905,9000,9072,9157,9229,9341,9454,9541,9617,9690,9776,9864,9951,10065,10198,10266,10337,10394,10557,10627,10684,10763,10840,10920,10999,11126,11252,11366,11454,11531,11615,11686", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "127,219,302,398,484,567,649,787,882,983,1074,1198,1254,1369,1433,1604,1787,1927,2019,2125,2220,2319,2487,2597,2697,2975,3088,3227,3428,3647,3748,3871,4067,4168,4229,4295,4377,4477,4578,4655,4756,4844,4954,5167,5235,5304,5383,5510,5584,5669,5731,5810,5881,5949,6064,6151,6258,6355,6430,6512,6581,6672,6732,6837,6944,7048,7178,7238,7338,7446,7580,7651,7743,7844,7904,7967,8045,8163,8332,8601,8900,8995,9067,9152,9224,9336,9449,9536,9612,9685,9771,9859,9946,10060,10193,10261,10332,10389,10552,10622,10679,10758,10835,10915,10994,11121,11247,11361,11449,11526,11610,11681,11845"}, "to": {"startLines": "189,190,191,261,273,274,275,292,305,307,308,338,356,358,361,365,366,367,370,372,374,375,376,377,378,379,380,381,382,383,384,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,433,437,447,448,449,450,451,452,453,454,455,464,465,466,467,468,469,470,471,472,473,474,475,476,477,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17448,17525,17617,23205,25005,25091,25174,27202,28448,28591,28692,31189,32946,33065,33303,33609,33780,33963,34260,34452,34659,34754,34853,35021,35131,35231,35509,35622,35761,35962,36181,36372,36495,36691,36792,36853,36919,37001,37101,37202,37279,37380,37468,37578,37791,37859,37928,38007,38134,38208,38293,38355,38434,38505,38573,39128,39215,39322,39419,39494,39576,39645,39736,39796,39901,40008,40112,40242,40302,40402,40510,40644,40929,41240,42791,42851,42914,42992,43110,43279,43548,43847,43942,44570,44655,44727,44839,44952,45039,45115,45188,45274,45362,45449,45563,45696,45764,45914,45971,46134,46204,46261,46340,46417,46497,46576,46703,46829,46943,47031,47108,47192,47263", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "17520,17612,17695,23296,25086,25169,25251,27335,28538,28687,28778,31308,32997,33175,33362,33775,33958,34098,34347,34553,34749,34848,35016,35126,35226,35504,35617,35756,35957,36176,36277,36490,36686,36787,36848,36914,36996,37096,37197,37274,37375,37463,37573,37786,37854,37923,38002,38129,38203,38288,38350,38429,38500,38568,38683,39210,39317,39414,39489,39571,39640,39731,39791,39896,40003,40107,40237,40297,40397,40505,40639,40710,41016,41336,42846,42909,42987,43105,43274,43543,43842,43937,44009,44650,44722,44834,44947,45034,45110,45183,45269,45357,45444,45558,45691,45759,45830,45966,46129,46199,46256,46335,46412,46492,46571,46698,46824,46938,47026,47103,47187,47258,47422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "52,77,82", "startColumns": "4,4,4", "startOffsets": "4940,7857,8236", "endColumns": "75,81,73", "endOffsets": "5011,7934,8305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "50,51,74,76,78,99,100,160,161,163,164,167,168,172,496,497,498", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4759,4854,7567,7758,7939,10169,10252,15108,15199,15365,15437,15734,15819,16155,47596,47672,47739", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4849,4935,7659,7852,8020,10247,10344,15194,15281,15432,15501,15814,15904,16226,47667,47734,47847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "71,75,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7278,7664,8719,8836,8975,9122,9253,9383,9527,9628,9762,9906", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "7377,7753,8831,8970,9117,9248,9378,9522,9623,9757,9901,10024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "197,207,209,210,211,215,223,226,229,233,237,241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18048,18866,19008,19070,19152,19440,19972,20163,20403,20737,21089,21386", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "18125,18922,19065,19147,19211,19508,20026,20229,20473,20800,21146,21453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "499,500", "startColumns": "4,4", "startOffsets": "47852,47935", "endColumns": "82,84", "endOffsets": "47930,48015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5016,5121,5269,5396,5504,5671,5801,5923,6173,6343,6451,6615,6745,6902,7059,7128,7194", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "5116,5264,5391,5499,5666,5796,5918,6023,6338,6446,6610,6740,6897,7054,7123,7189,7273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "73,84,85,86", "startColumns": "4,4,4,4", "startOffsets": "7452,8402,8501,8613", "endColumns": "114,98,111,105", "endOffsets": "7562,8496,8608,8714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "72,81,162,166,495,501,502", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7382,8149,15286,15583,47427,48020,48107", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "7447,8231,15360,15729,47591,48102,48183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,324,419,633,680,751,852,926,1146,1206,1298,1370,1427,1491,1563,1651,1971,2043,2110,2164,2221,2305,2437,2551,2609,2698,2779,2868,2934,3035,3338,3415,3493,3556,3617,3679,3740,3814,3897,3997,4098,4188,4291,4388,4463,4542,4628,4842,5053,5168,5294,5350,6002,6106,6171", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "166,319,414,628,675,746,847,921,1141,1201,1293,1365,1422,1486,1558,1646,1966,2038,2105,2159,2216,2300,2432,2546,2604,2693,2774,2863,2929,3030,3333,3410,3488,3551,3612,3674,3735,3809,3892,3992,4093,4183,4286,4383,4458,4537,4623,4837,5048,5163,5289,5345,5997,6101,6166,6221"}, "to": {"startLines": "258,259,260,262,266,267,268,269,270,271,272,288,291,293,297,298,303,309,310,318,328,331,332,333,334,335,341,344,349,351,352,353,354,357,359,360,364,368,369,371,373,385,410,411,412,413,414,432,439,440,441,442,444,445,446,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22841,22957,23110,23301,24340,24387,24458,24559,24633,24853,24913,26876,27145,27340,27644,27716,28078,28783,28855,29397,30328,30536,30620,30752,30866,30924,31477,31717,32143,32282,32383,32686,32763,33002,33180,33241,33548,34103,34177,34352,34558,36282,38688,38791,38888,38963,39042,40715,41395,41606,41721,41847,41970,42622,42726,44515", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "22952,23105,23200,23510,24382,24453,24554,24628,24848,24908,25000,26943,27197,27399,27711,27799,28393,28850,28917,29446,30380,30615,30747,30861,30919,31008,31553,31801,32204,32378,32681,32758,32836,33060,33236,33298,33604,34172,34255,34447,34654,36367,38786,38883,38958,39037,39123,40924,41601,41716,41842,41898,42617,42721,42786,44565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3061,3170", "endColumns": "108,121", "endOffsets": "3165,3287"}}]}]}