{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,18463", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,18541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,211", "endColumns": "75,79,76", "endOffsets": "126,206,283"}, "to": {"startLines": "54,102,107", "startColumns": "4,4,4", "startOffsets": "4991,10522,10908", "endColumns": "75,79,76", "endOffsets": "5062,10597,10980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,491,660,744", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "173,261,339,486,655,739,820"}, "to": {"startLines": "97,106,187,191,201,207,208", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10054,10820,17851,18146,19035,19635,19719", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "10122,10903,17924,18288,19199,19714,19795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3140,3249", "endColumns": "108,121", "endOffsets": "3244,3366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,104,105,108,123,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,190,195,196,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3371,3446,3521,3599,3691,4511,4603,4731,10694,10755,10985,12754,13003,13068,13158,13222,13288,13342,13411,13471,13525,13642,13702,13764,13818,13890,14020,14107,14187,14283,14367,14459,14598,14667,14745,14876,14964,15044,15098,15149,15215,15287,15364,15435,15517,15589,15666,15739,15810,15915,16003,16075,16167,16263,16337,16411,16507,16559,16641,16708,16795,16882,16944,17008,17071,17139,17245,17352,17450,17567,17625,18067,18546,18629,18776", "endLines": "7,37,38,39,40,41,49,50,51,104,105,108,123,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,190,195,196,198", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "423,3441,3516,3594,3686,3769,4598,4726,4807,10750,10815,11079,12825,13063,13153,13217,13283,13337,13406,13466,13520,13637,13697,13759,13813,13885,14015,14102,14182,14278,14362,14454,14593,14662,14740,14871,14959,15039,15093,15144,15210,15282,15359,15430,15512,15584,15661,15734,15805,15910,15998,16070,16162,16258,16332,16406,16502,16554,16636,16703,16790,16877,16939,17003,17066,17134,17240,17347,17445,17562,17620,17675,18141,18624,18699,18847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "86", "startColumns": "4", "startOffsets": "8680", "endColumns": "138", "endOffsets": "8814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,378,527,663,792,927,1060,1158,1293,1426", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "156,246,373,522,658,787,922,1055,1153,1288,1421,1535"}, "to": {"startLines": "96,100,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9948,10329,11404,11531,11680,11816,11945,12080,12213,12311,12446,12579", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "10049,10414,11526,11675,11811,11940,12075,12208,12306,12441,12574,12688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "122,209", "startColumns": "4,4", "startOffsets": "12693,19800", "endColumns": "60,75", "endOffsets": "12749,19871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3774,3870,3972,4073,4171,4281,4389,18852", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3865,3967,4068,4166,4276,4384,4506,18948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "78,79,80,81,82,83,84,85,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7681,7787,7947,8071,8181,8337,8465,8578,8819,8988,9099,9269,9399,9562,9726,9794,9861", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "7782,7942,8066,8176,8332,8460,8573,8675,8983,9094,9264,9394,9557,9721,9789,9856,9943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "98,109,110,111", "startColumns": "4,4,4,4", "startOffsets": "10127,11084,11187,11302", "endColumns": "106,102,114,101", "endOffsets": "10229,11182,11297,11399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,373,524,636,728,822,901,990,1085,1200,1313,1408,1503,1608,1728,1810,1894,2088,2185,2290,2405,2515", "endColumns": "177,139,150,111,91,93,78,88,94,114,112,94,94,104,119,81,83,193,96,104,114,109,153", "endOffsets": "228,368,519,631,723,817,896,985,1080,1195,1308,1403,1498,1603,1723,1805,1889,2083,2180,2285,2400,2510,2664"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5067,5245,5385,5536,5648,5740,5834,5913,6002,6097,6212,6325,6420,6515,6620,6740,6822,6906,7100,7197,7302,7417,7527", "endColumns": "177,139,150,111,91,93,78,88,94,114,112,94,94,104,119,81,83,193,96,104,114,109,153", "endOffsets": "5240,5380,5531,5643,5735,5829,5908,5997,6092,6207,6320,6415,6510,6615,6735,6817,6901,7095,7192,7297,7412,7522,7676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "200", "startColumns": "4", "startOffsets": "18953", "endColumns": "81", "endOffsets": "19030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "205,206", "startColumns": "4,4", "startOffsets": "19464,19548", "endColumns": "83,86", "endOffsets": "19543,19630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "52,53,99,101,103,124,125,185,186,188,189,192,193,197,202,203,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4812,4907,10234,10419,10602,12830,12909,17680,17770,17929,17998,18293,18376,18704,19204,19282,19350", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "4902,4986,10324,10517,10689,12904,12998,17765,17846,17993,18062,18371,18458,18771,19277,19345,19459"}}]}]}