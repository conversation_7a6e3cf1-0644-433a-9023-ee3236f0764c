{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,18689", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,18767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4956,10511,10906", "endColumns": "77,82,77", "endOffsets": "5029,10589,10979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3383,3459,3527,4238,4929,5008,5138,5245,5300,5398,5490,5575,5685,5798,5857,5943,5994,6074,6184,6262,6335,6402,6468,6516,6597,6703,6776,6822,6872,6943,7001,7065,7265,7412,7557,7629,7715,7799,7894,7984,8082,8160,8253,8334,8434,8519,8616,8671,8791,8842,8904,8980,9050,9129,9199,9270,9364,9439,9492", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3378,3454,3522,4233,4924,5003,5133,5240,5295,5393,5485,5570,5680,5793,5852,5938,5989,6069,6179,6257,6330,6397,6463,6511,6592,6698,6771,6817,6867,6938,6996,7060,7260,7407,7552,7624,7710,7794,7889,7979,8077,8155,8248,8329,8429,8514,8611,8666,8786,8837,8899,8975,9045,9124,9194,9265,9359,9434,9487,9559"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19726,19801,19885,19947,20023,20108,20168,20470,20528,20622,20703,20957,21163,21276,21432,21797,22088,23121,23372,23467,23539,23699,23787,23875,24045,24353,24445,24524,24623,24701,24799,24893,24984,25082,25200,25353,25624,25727,26490,26566,26634,28296,28987,29066,29196,29303,29358,29456,29548,29633,29743,29856,29915,30540,30591,30671,30945,31023,31096,31163,31670,32044,32125,32231,32304,32350,32680,32751,32809,32873,33073,33220,33563,33635,34616,34700,34877,34967,35375,35453,35616,36273,36845,36930,44643,44698,44818,44979,45543,47801,47871,47950,48020,48091,48185,48260,49681", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "19796,19880,19942,20018,20103,20163,20222,20523,20617,20698,20792,21065,21271,21359,21505,21876,22167,23217,23462,23534,23626,23782,23870,23978,24122,24440,24519,24618,24696,24794,24888,24979,25077,25195,25271,25440,25722,25818,26561,26629,27340,28982,29061,29191,29298,29353,29451,29543,29628,29738,29851,29910,29996,30586,30666,30776,31018,31091,31158,31224,31713,32120,32226,32299,32345,32395,32746,32804,32868,33068,33215,33360,33630,33716,34695,34790,34962,35060,35448,35541,35692,36368,36925,37022,44693,44813,44864,45036,45614,47866,47945,48015,48086,48180,48255,48308,49748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,327,422,628,674,746,849,924,1165,1227,1320,1396,1453,1517,1591,1681,1978,2051,2118,2186,2245,2333,2468,2580,2638,2727,2809,2912,2982,3082,3403,3480,3558,3639,3714,3776,3837,3909,3995,4105,4211,4301,4410,4504,4580,4659,4745,4927,5132,5241,5367,5429,6200,6311,6376", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "164,322,417,623,669,741,844,919,1160,1222,1315,1391,1448,1512,1586,1676,1973,2046,2113,2181,2240,2328,2463,2575,2633,2722,2804,2907,2977,3077,3398,3475,3553,3634,3709,3771,3832,3904,3990,4100,4206,4296,4405,4499,4575,4654,4740,4922,5127,5236,5362,5424,6195,6306,6371,6429"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25823,25937,26095,26284,27345,27391,27463,27566,27641,27882,27944,30001,30285,30476,30781,30855,31229,31904,31977,32548,33504,33806,33894,34029,34141,34199,34795,35065,35546,35697,35797,36118,36195,36441,36644,36719,37027,37605,37677,37862,38072,39842,42289,42398,42492,42568,42647,44362,45041,45246,45355,45481,45619,46390,46501,48313", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "25932,26090,26185,26485,27386,27458,27561,27636,27877,27939,28032,30072,30337,30535,30850,30940,31521,31972,32039,32611,33558,33889,34024,34136,34194,34283,34872,35163,35611,35792,36113,36190,36268,36517,36714,36776,37083,37672,37758,37967,38173,39927,42393,42487,42563,42642,42728,44539,45241,45350,45476,45538,46385,46496,46561,48366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,365,518,633,718,807,883,974,1063,1177,1292,1381,1470,1578,1701,1783,1869,2058,2147,2250,2365,2481", "endColumns": "166,142,152,114,84,88,75,90,88,113,114,88,88,107,122,81,85,188,88,102,114,115,132", "endOffsets": "217,360,513,628,713,802,878,969,1058,1172,1287,1376,1465,1573,1696,1778,1864,2053,2142,2245,2360,2476,2609"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5034,5201,5344,5497,5612,5697,5786,5862,5953,6042,6156,6271,6360,6449,6557,6680,6762,6848,7037,7126,7229,7344,7460", "endColumns": "166,142,152,114,84,88,75,90,88,113,114,88,88,107,122,81,85,188,88,102,114,115,132", "endOffsets": "5196,5339,5492,5607,5692,5781,5857,5948,6037,6151,6266,6355,6444,6552,6675,6757,6843,7032,7121,7224,7339,7455,7588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "33721", "endColumns": "84", "endOffsets": "33801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4778,4874,10216,10408,10594,12899,12978,17879,17971,18139,18212,18512,18598,18931,51465,51547,51617", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4869,4951,10309,10506,10678,12973,13066,17966,18053,18207,18277,18593,18684,19003,51542,51612,51733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3739,3838,3940,4040,4138,4245,4351,19085", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3833,3935,4035,4133,4240,4346,4466,19181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7593,7701,7864,7995,8103,8264,8397,8519,8789,8981,9090,9255,9387,9552,9709,9776,9845", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "7696,7859,7990,8098,8259,8392,8514,8624,8976,9085,9250,9382,9547,9704,9771,9840,9925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10039,10810,18058,18365,51296,51940,52028", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "10104,10901,18134,18507,51460,52023,52105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3295,3376,3455,3542,3643,4471,4575,4697,10683,10745,10984,12818,13071,13134,13223,13287,13356,13419,13493,13557,13614,13732,13790,13852,13909,13989,14128,14217,14293,14388,14469,14551,14692,14773,14853,15004,15094,15174,15230,15286,15352,15431,15513,15584,15673,15747,15824,15894,15973,16073,16157,16241,16333,16433,16507,16588,16690,16743,16828,16895,16988,17077,17139,17203,17266,17334,17447,17554,17658,17759,17819,18282,18772,18855,19008", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3371,3450,3537,3638,3734,4570,4692,4773,10740,10805,11074,12894,13129,13218,13282,13351,13414,13488,13552,13609,13727,13785,13847,13904,13984,14123,14212,14288,14383,14464,14546,14687,14768,14848,14999,15089,15169,15225,15281,15347,15426,15508,15579,15668,15742,15819,15889,15968,16068,16152,16236,16328,16428,16502,16583,16685,16738,16823,16890,16983,17072,17134,17198,17261,17329,17442,17549,17653,17754,17814,17874,18360,18850,18926,19080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19186,19273,19377,19443,19510,19576,19658", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "19268,19372,19438,19505,19571,19653,19721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,258,378,521,671,817,953,1099,1199,1345,1487", "endColumns": "108,93,119,142,149,145,135,145,99,145,141,127", "endOffsets": "159,253,373,516,666,812,948,1094,1194,1340,1482,1610"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9930,10314,11400,11520,11663,11813,11959,12095,12241,12341,12487,12629", "endColumns": "108,93,119,142,149,145,135,145,99,145,141,127", "endOffsets": "10034,10403,11515,11658,11808,11954,12090,12236,12336,12482,12624,12752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8629", "endColumns": "159", "endOffsets": "8784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "51738,51838", "endColumns": "99,101", "endOffsets": "51833,51935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20797,21070,21364,21510,21603,21675,22172,22245,22387,22449,22517,22588,22648,22709,22783,22908,22977,23222,24127,24200,25276,25445,25531,30077,30194,31526,32400,32460,32616,33365,33435,34288,34399,35168,35272", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "20859,21158,21427,21598,21670,21731,22240,22307,22444,22512,22583,22643,22704,22778,22842,22972,23035,23292,24195,24276,25348,25526,25619,30189,30280,31571,32455,32543,32675,33430,33499,34394,34483,35267,35370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10109,11079,11180,11295", "endColumns": "106,100,114,104", "endOffsets": "10211,11175,11290,11395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3059,3173", "endColumns": "113,121", "endOffsets": "3168,3290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,219,298,392,484,568,651,785,879,973,1065,1193,1261,1383,1447,1619,1808,1964,2063,2163,2260,2360,2561,2666,2767,3038,3153,3296,3503,3729,3827,3954,4147,4241,4302,4368,4449,4555,4664,4746,4846,4940,5045,5263,5331,5402,5478,5603,5675,5759,5834,5928,5999,6067,6184,6272,6392,6491,6568,6663,6732,6820,6881,6987,7098,7198,7337,7406,7503,7607,7742,7813,7912,8022,8080,8136,8222,8320,8498,8780,9078,9176,9257,9347,9414,9531,9637,9722,9810,9883,9966,10055,10152,10280,10425,10496,10567,10620,10784,10849,10914,10995,11068,11148,11227,11372,11494,11604,11694,11782,11867,11943", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "125,214,293,387,479,563,646,780,874,968,1060,1188,1256,1378,1442,1614,1803,1959,2058,2158,2255,2355,2556,2661,2762,3033,3148,3291,3498,3724,3822,3949,4142,4236,4297,4363,4444,4550,4659,4741,4841,4935,5040,5258,5326,5397,5473,5598,5670,5754,5829,5923,5994,6062,6179,6267,6387,6486,6563,6658,6727,6815,6876,6982,7093,7193,7332,7401,7498,7602,7737,7808,7907,8017,8075,8131,8217,8315,8493,8775,9073,9171,9252,9342,9409,9526,9632,9717,9805,9878,9961,10050,10147,10275,10420,10491,10562,10615,10779,10844,10909,10990,11063,11143,11222,11367,11489,11599,11689,11777,11862,11938,12105"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20227,20302,20391,26190,28037,28129,28213,30342,31576,31718,31812,34488,36373,36522,36781,37088,37260,37449,37763,37972,38178,38275,38375,38576,38681,38782,39053,39168,39311,39518,39744,39932,40059,40252,40346,40407,40473,40554,40660,40769,40851,40951,41045,41150,41368,41436,41507,41583,41708,41780,41864,41939,42033,42104,42172,42733,42821,42941,43040,43117,43212,43281,43369,43430,43536,43647,43747,43886,43955,44052,44156,44291,44544,44869,46566,46624,46680,46766,46864,47042,47324,47622,47720,48371,48461,48528,48645,48751,48836,48924,48997,49080,49169,49266,49394,49539,49610,49753,49806,49970,50035,50100,50181,50254,50334,50413,50558,50680,50790,50880,50968,51053,51129", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "20297,20386,20465,26279,28124,28208,28291,30471,31665,31807,31899,34611,36436,36639,36840,37255,37444,37600,37857,38067,38270,38370,38571,38676,38777,39048,39163,39306,39513,39739,39837,40054,40247,40341,40402,40468,40549,40655,40764,40846,40946,41040,41145,41363,41431,41502,41578,41703,41775,41859,41934,42028,42099,42167,42284,42816,42936,43035,43112,43207,43276,43364,43425,43531,43642,43742,43881,43950,44047,44151,44286,44357,44638,44974,46619,46675,46761,46859,47037,47319,47617,47715,47796,48456,48523,48640,48746,48831,48919,48992,49075,49164,49261,49389,49534,49605,49676,49801,49965,50030,50095,50176,50249,50329,50408,50553,50675,50785,50875,50963,51048,51124,51291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20864,21736,21881,21943,22024,22312,22847,23040,23297,23631,23983,24281", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "20952,21792,21938,22019,22083,22382,22903,23116,23367,23694,24040,24348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "12757,52110", "endColumns": "60,77", "endOffsets": "12813,52183"}}]}]}