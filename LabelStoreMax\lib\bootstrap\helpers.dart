// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'dart:convert';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode performance optimization
// Status alert imports removed - functionality disabled
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// WP JSON API imports removed - will be replaced with WooCommerce authentication

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/billing_details.dart';
import '/config/payment_gateways.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/app/models/default_shipping.dart';
import '/app/services/auth_service.dart';
import '/app/services/woocommerce_service.dart';
import '/app/models/customer_address.dart';
import '/app/models/customer_country.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/payment_type.dart';
import '/bootstrap/app_helper.dart';
// import '/bootstrap/enums/symbol_position_enums.dart'; // Unused after MoneyFormatter replacement
import '/bootstrap/extensions.dart';
import '/bootstrap/shared_pref/shared_key.dart';
// import '/config/currency.dart'; // Unused after MoneyFormatter replacement

import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:html/parser.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:nylo_framework/nylo_framework.dart';
// WP JSON API response imports removed
import '../resources/themes/styles/color_styles.dart';
import 'package:flutter/services.dart' show rootBundle;

/// Get current authenticated user from WooCommerce/JWT
Future<WooCustomer?> getCurrentUser() async {
  try {
    final authService = AuthService();
    final userSession = await authService.getCurrentUser();

    if (userSession != null && userSession.id != null) {
      // Get full customer data from WooCommerce API
      final wooService = WooCommerceService();
      final customer = await wooService.getCustomer(int.parse(userSession.id!));
      return customer;
    }
    return null;
  } catch (e) {
    print('Error getting current user: $e');
    return null;
  }
}

/// Check if user is currently logged in
Future<bool> isUserLoggedIn() async {
  try {
    final authService = AuthService();
    final userSession = await authService.getCurrentUser();
    return userSession != null && userSession.id != null;
  } catch (e) {
    print('Error checking login status: $e');
    return false;
  }
}

/// Get current user ID
Future<String?> getCurrentUserId() async {
  try {
    final authService = AuthService();
    final userSession = await authService.getCurrentUser();
    return userSession?.id;
  } catch (e) {
    print('Error getting current user ID: $e');
    return null;
  }
}

// WooSignal function removed - will be replaced with WooCommerce service

/// helper to find correct color from the [context].
class ThemeColor {
  static ColorStyles get(BuildContext context, {String? themeId}) =>
      nyColorStyle<ColorStyles>(context, themeId: themeId);

  static Color fromHex(String hexColor) => nyHexColor(hexColor);
}

/// helper to set colors on TextStyle
extension ColorsHelper on TextStyle {
  TextStyle setColor(
      BuildContext context, Color Function(ColorStyles? color) newColor) {
    return copyWith(color: newColor(ThemeColor.get(context)));
  }
}

Future<List<PaymentType?>> getPaymentTypes() async {
  print('=== Getting Payment Types ===');

  // Try to get dynamic payment gateways first
  try {
    final dynamicGateways = await getDynamicPaymentGateways();
    if (dynamicGateways.isNotEmpty) {
      print('✅ Using ${dynamicGateways.length} dynamic payment gateways');
      return dynamicGateways.cast<PaymentType?>();
    }
  } catch (e) {
    print('❌ Error getting dynamic payment gateways: $e');
  }

  // Fallback to static payment types
  print('⚠️ Falling back to static payment types');
  List<PaymentType?> paymentTypes = [];
  for (var appPaymentGateway in appPaymentGateways) {
    if (paymentTypes.firstWhere(
            (paymentType) => paymentType!.name != appPaymentGateway,
            orElse: () => null) ==
        null) {
      paymentTypes.add(paymentTypeList.firstWhereOrNull(
          (paymentTypeList) => paymentTypeList.name == appPaymentGateway));
    }
  }

  if (!appPaymentGateways.contains('CashOnDelivery') &&
      AppHelper.instance.appConfig!.codEnabled == true) {
    paymentTypes.add(paymentTypeList
        .firstWhereOrNull((element) => element.name == "CashOnDelivery"));
  }

  return paymentTypes.where((v) => v != null).toList();
}

PaymentType addPayment(
        {required int id,
        required String name,
        required String description,
        required String assetImage,
        required Function pay}) =>
    PaymentType(
      id: id,
      name: name,
      desc: description,
      assetImage: assetImage,
      pay: pay,
    );

showStatusAlert(context,
    {required String title,
    required String subtitle,
    IconData? icon,
    int? duration}) {
  // StatusAlert functionality disabled - using SnackBar instead
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: TextStyle(fontWeight: FontWeight.bold)),
          Text(subtitle),
        ],
      ),
      duration: Duration(seconds: duration ?? 2),
    ),
  );
}

String parseHtmlString(String? htmlString) {
  var document = parse(htmlString);
  return parse(document.body!.text).documentElement!.text;
}

String moneyFormatter(double amount) {
  // Simple money formatter implementation
  String symbol = AppHelper.instance.appConfig?.currencyMeta?['symbolNative'] ?? 'LYD';
  int decimalDigits = AppHelper.instance.appConfig?.currencyMeta?['decimalDigits'] ?? 2;

  // For now, always place symbol on left (can be enhanced later)
  return '$symbol${amount.toStringAsFixed(decimalDigits)}';
}

String formatDoubleCurrency({required double total}) {
  return moneyFormatter(total);
}

String formatStringCurrency({required dynamic total}) {
  double tmpVal = 0;
  if (total != null) {
    if (total is num) {
      tmpVal = total.toDouble(); // Guaranteed: Handle num directly
    } else if (total is String && total != "") {
      tmpVal = parseWcPrice(total) ?? 0.0; // Handle nullable return
    }
  }
  return moneyFormatter(tmpVal);
}

String workoutSaleDiscount(
    {required dynamic salePrice, required dynamic priceBefore}) {
  double dSalePrice = 0;
  double dPriceBefore = 0;

  if (salePrice is num) {
    dSalePrice = salePrice.toDouble();
  } else if (salePrice is String) {
    dSalePrice = parseWcPrice(salePrice) ?? 0.0; // Handle nullable return
  }

  if (priceBefore is num) {
    dPriceBefore = priceBefore.toDouble();
  } else if (priceBefore is String) {
    dPriceBefore = parseWcPrice(priceBefore) ?? 0.0; // Handle nullable return
  }

  // Prevent division by zero and ensure valid calculation
  if (dPriceBefore <= 0 || dSalePrice < 0 || dSalePrice >= dPriceBefore) {
    return "0"; // Return 0% discount for invalid cases
  }

  double discount = ((dPriceBefore - dSalePrice) * 100) / dPriceBefore;

  // Ensure the result is finite and not NaN
  if (!discount.isFinite || discount.isNaN) {
    return "0";
  }

  return discount.toStringAsFixed(0);
}

openBrowserTab({required String url}) async {
  try {
    await InAppBrowser.openWithSystemBrowser(url: WebUri(url));
  } catch (e) {
    printError('Error opening InAppWebView: $e');
  }
}

bool isNumeric(String? str) {
  if (str == null) {
    return false;
  }
  return double.tryParse(str) != null;
}

checkout(
    Function(String total, BillingDetails? billingDetails, Cart cart)
        completeCheckout) async {
  String cartTotal = await CheckoutSession.getInstance
      .total(withFormat: false);
  BillingDetails? billingDetails = CheckoutSession.getInstance.billingDetails;
  Cart cart = Cart.getInstance;
  return await completeCheckout(cartTotal, billingDetails, cart);
}

double? strCal({required String sum}) {
  if (sum == "") {
    return 0;
  }
  GrammarParser p = GrammarParser();
  Expression exp = p.parse(sum);
  ContextModel cm = ContextModel();
  return exp.evaluate(EvaluationType.REAL, cm);
}

Future<double?> workoutShippingCostWC({required String? sum, List<CartLineItem>? cartItems}) async {
  if (sum == null || sum == "") {
    return 0;
  }
  // Use provided cart items or get from cache/storage
  List<CartLineItem> cartLineItem = cartItems ?? await Cart.getInstance.getCart();
  sum = sum.replaceAllMapped(defaultRegex(r'\[qty\]', strict: true), (replace) {
    return cartLineItem
        .map((f) => f.quantity)
        .toList()
        .reduce((i, d) => i + d)
        .toString();
  });

  String orderTotal = await Cart.getInstance.getSubtotal();

  sum = sum.replaceAllMapped(defaultRegex(r'\[fee(.*)]'), (replace) {
    if (replace.groupCount < 1) {
      return "()";
    }
    String newSum = replace.group(1)!;

    // PERCENT
    String percentVal = newSum.replaceAllMapped(
        defaultRegex(r'percent="([0-9\.]+)"'), (replacePercent) {
      if (replacePercent.groupCount >= 1) {
        String strPercentage =
            "( ($orderTotal * ${replacePercent.group(1)}) / 100 )";
        double? calPercentage = strCal(sum: strPercentage);

        // MIN
        String strRegexMinFee = r'min_fee="([0-9\.]+)"';
        if (defaultRegex(strRegexMinFee).hasMatch(newSum)) {
          String strMinFee =
              defaultRegex(strRegexMinFee).firstMatch(newSum)!.group(1) ?? "0";
          double doubleMinFee = double.parse(strMinFee);

          if (calPercentage! < doubleMinFee) {
            return "($doubleMinFee)";
          }
          newSum = newSum.replaceAll(defaultRegex(strRegexMinFee), "");
        }

        // MAX
        String strRegexMaxFee = r'max_fee="([0-9\.]+)"';
        if (defaultRegex(strRegexMaxFee).hasMatch(newSum)) {
          String strMaxFee =
              defaultRegex(strRegexMaxFee).firstMatch(newSum)!.group(1) ?? "0";
          double doubleMaxFee = double.parse(strMaxFee);

          if (calPercentage! > doubleMaxFee) {
            return "($doubleMaxFee)";
          }
          newSum = newSum.replaceAll(defaultRegex(strRegexMaxFee), "");
        }
        return "($calPercentage)";
      }
      return "";
    });

    percentVal = percentVal
        .replaceAll(
            defaultRegex(r'(min_fee=\"([0-9\.]+)\"|max_fee=\"([0-9\.]+)\")'),
            "")
        .trim();
    return percentVal;
  });

  return strCal(sum: sum);
}

Future<double?> workoutShippingClassCostWC(
    {required String? sum, List<CartLineItem>? cartLineItem}) async {
  if (sum == null || sum == "") {
    return 0;
  }
  sum = sum.replaceAllMapped(defaultRegex(r'\[qty\]', strict: true), (replace) {
    return cartLineItem!
        .map((f) => f.quantity)
        .toList()
        .reduce((i, d) => i + d)
        .toString();
  });

  String orderTotal = await Cart.getInstance.getSubtotal();

  sum = sum.replaceAllMapped(defaultRegex(r'\[fee(.*)]'), (replace) {
    if (replace.groupCount < 1) {
      return "()";
    }
    String newSum = replace.group(1)!;

    // PERCENT
    String percentVal = newSum.replaceAllMapped(
        defaultRegex(r'percent="([0-9\.]+)"'), (replacePercent) {
      if (replacePercent.groupCount >= 1) {
        String strPercentage =
            "( ($orderTotal * ${replacePercent.group(1)}) / 100 )";
        double? calPercentage = strCal(sum: strPercentage);

        // MIN
        String strRegexMinFee = r'min_fee="([0-9\.]+)"';
        if (defaultRegex(strRegexMinFee).hasMatch(newSum)) {
          String strMinFee =
              defaultRegex(strRegexMinFee).firstMatch(newSum)!.group(1) ?? "0";
          double doubleMinFee = double.parse(strMinFee);

          if (calPercentage! < doubleMinFee) {
            return "($doubleMinFee)";
          }
          newSum = newSum.replaceAll(defaultRegex(strRegexMinFee), "");
        }

        // MAX
        String strRegexMaxFee = r'max_fee="([0-9\.]+)"';
        if (defaultRegex(strRegexMaxFee).hasMatch(newSum)) {
          String strMaxFee =
              defaultRegex(strRegexMaxFee).firstMatch(newSum)!.group(1) ?? "0";
          double doubleMaxFee = double.parse(strMaxFee);

          if (calPercentage! > doubleMaxFee) {
            return "($doubleMaxFee)";
          }
          newSum = newSum.replaceAll(defaultRegex(strRegexMaxFee), "");
        }
        return "($calPercentage)";
      }
      return "";
    });

    percentVal = percentVal
        .replaceAll(
            defaultRegex(r'(min_fee=\"([0-9\.]+)\"|max_fee=\"([0-9\.]+)\")'),
            "")
        .trim();
    return percentVal;
  });

  return strCal(sum: sum);
}

RegExp defaultRegex(
  String pattern, {
  bool? strict,
}) {
  return RegExp(
    pattern,
    caseSensitive: strict ?? false,
    multiLine: false,
  );
}

bool isEmail(String em) {
  String p =
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
  RegExp regExp = RegExp(p);
  return regExp.hasMatch(em);
}

navigatorPush(BuildContext context,
    {required String routeName,
    Object? arguments,
    bool forgetAll = false,
    int? forgetLast}) {
  if (forgetAll) {
    Navigator.of(context).pushNamedAndRemoveUntil(
        routeName, (Route<dynamic> route) => false,
        arguments: arguments);
  }
  if (forgetLast != null) {
    int count = 0;
    Navigator.of(context).popUntil((route) {
      return count++ == forgetLast;
    });
  }
  Navigator.of(context).pushNamed(routeName, arguments: arguments);
}

DateTime parseDateTime(String strDate) => DateTime.parse(strDate);

DateFormat formatDateTime(String format) => DateFormat(format);

String dateFormatted({required String date, required String formatType}) =>
    formatDateTime(formatType).format(parseDateTime(date));

enum FormatType {
  dateTime,
  date,
  time,
}

String formatForDateTime(FormatType formatType) {
  switch (formatType) {
    case FormatType.date:
      {
        return "yyyy-MM-dd";
      }
    case FormatType.dateTime:
      {
        return "dd-MM-yyyy hh:mm a";
      }
    case FormatType.time:
      {
        return "hh:mm a";
      }
  }
}

// SURGICAL FIX: Enhanced parseWcPrice with proper null handling for missing data
double? parseWcPrice(String? price) {
  // THE SURGICAL FIX: Return null for missing data.
  // This prevents empty strings from being incorrectly converted to 0.0.
  if (price == null || price.isEmpty) {
    return null; // Return null for missing data.
  }

  // Only return 0.0 for an explicit "0".
  String priceString = price;
  if (priceString == "0") {
    return 0.0;
  }

  // Store original value for debugging
  String originalValue = priceString;

  // PHASE 13: Comprehensive string cleaning (same as _parsePrice methods)
  priceString = priceString.trim(); // 1. Trim whitespace
  priceString = priceString.replaceAll('د.ل', '').trim(); // 2. Remove Arabic currency symbol
  priceString = priceString.replaceAll('LYD', '').trim(); // 3. Remove LYD currency code
  priceString = priceString.replaceAll('ل.د', '').trim(); // 4. Remove alternative Arabic currency
  priceString = priceString.replaceAll(RegExp(r'[^\d.,]'), ''); // 5. Remove all non-numeric chars except . and ,

  // Handle thousand separators and decimal normalization
  if (priceString.contains(',') && priceString.contains('.')) {
    // If both comma and period exist, assume comma is thousands separator
    priceString = priceString.replaceAll(',', ''); // Remove thousand separators
  } else if (priceString.contains(',') && !priceString.contains('.')) {
    // If only comma exists, it might be decimal separator (European format)
    priceString = priceString.replaceAll(',', '.'); // Convert comma decimal to period
  }

  // Final validation - ensure only one decimal point
  List<String> parts = priceString.split('.');
  if (parts.length > 2) {
    // Multiple decimal points - keep only the last one as decimal
    priceString = parts.sublist(0, parts.length - 1).join('') + '.' + parts.last;
  }

  double? parsed = double.tryParse(priceString);

  if (parsed == null) {
    if (kDebugMode) {
      print('⚠️ parseWcPrice: Failed to parse "$originalValue" (cleaned: "$priceString"), returning 0.0');
    }
    return 0.0;
  }

  return parsed;
}

class UserAuth {
  UserAuth._privateConstructor();
  static final UserAuth instance = UserAuth._privateConstructor();

  String redirect = "/home";
}

Future<List<DefaultShipping>> getDefaultShipping() async {
  String data =
      await rootBundle.loadString('public/json/default_shipping.json');
  dynamic dataJson = json.decode(data);
  List<DefaultShipping> shipping = [];

  dataJson.forEach((key, value) {
    DefaultShipping defaultShipping =
        DefaultShipping(code: key, country: value['country'], states: []);
    if (value['states'] != null) {
      value['states'].forEach((key1, value2) {
        defaultShipping.states
            .add(DefaultShippingState(code: key1, name: value2));
      });
    }
    shipping.add(defaultShipping);
  });
  return shipping;
}

Future<DefaultShipping?> findCountryMetaForShipping(String countryCode) async {
  List<DefaultShipping> defaultShipping = await getDefaultShipping();
  List<DefaultShipping> shippingByCountryCode =
      defaultShipping.where((element) => element.code == countryCode).toList();
  if (shippingByCountryCode.isNotEmpty) {
    return shippingByCountryCode.first;
  }
  return null;
}

DefaultShippingState? findDefaultShippingStateByCode(
    DefaultShipping defaultShipping, String code) {
  List<DefaultShippingState> defaultShippingStates =
      defaultShipping.states.where((state) => state.code == code).toList();
  if (defaultShippingStates.isEmpty) {
    return null;
  }
  DefaultShippingState defaultShippingState = defaultShippingStates.first;
  return DefaultShippingState(
      code: defaultShippingState.code, name: defaultShippingState.name);
}

/// Check if WooCommerce customer has a specific meta key
bool hasKeyInCustomerMeta(WooCustomer customer, String key) {
  return (customer.metaData ?? [])
      .where((meta) => meta.key == key)
      .toList()
      .isNotEmpty;
}

/// Get meta value from WooCommerce customer by key
String? getCustomerMetaValue(WooCustomer customer, String key) {
  final metaItem = (customer.metaData ?? [])
      .firstWhereOrNull((meta) => meta.key == key);
  return metaItem?.value.toString();
}

/// Get customer billing information from WooCommerce customer
Map<String, String> getCustomerBillingInfo(WooCustomer customer) {
  return {
    'first_name': customer.billing?.firstName ?? '',
    'last_name': customer.billing?.lastName ?? '',
    'company': customer.billing?.company ?? '',
    'address_1': customer.billing?.address1 ?? '',
    'address_2': customer.billing?.address2 ?? '',
    'city': customer.billing?.city ?? '',
    'state': customer.billing?.state ?? '',
    'postcode': customer.billing?.postcode ?? '',
    'country': customer.billing?.country ?? '',
    'email': customer.billing?.email ?? customer.email ?? '',
    'phone': customer.billing?.phone ?? '',
  };
}

/// Get customer shipping information from WooCommerce customer
Map<String, String> getCustomerShippingInfo(WooCustomer customer) {
  return {
    'first_name': customer.shipping?.firstName ?? '',
    'last_name': customer.shipping?.lastName ?? '',
    'company': customer.shipping?.company ?? '',
    'address_1': customer.shipping?.address1 ?? '',
    'address_2': customer.shipping?.address2 ?? '',
    'city': customer.shipping?.city ?? '',
    'state': customer.shipping?.state ?? '',
    'postcode': customer.shipping?.postcode ?? '',
    'country': customer.shipping?.country ?? '',
  };
}

// String fetchValueInMeta(UserInfoResponse userInfoResponse, String key) {
//   String value = "";
//   List<dynamic>? metaDataValue = (userInfoResponse.data!.metaData ?? [])
//       .where((meta) => meta.key == key)
//       .first
//       .value;
//   if (metaDataValue != null && metaDataValue.isNotEmpty) {
//     return metaDataValue.first ?? "";
//   }
//   return value;
// }

String truncateString(String data, int length) {
  return (data.length >= length) ? '${data.substring(0, length)}...' : data;
}

Future<List<dynamic>> getWishlistProducts() async {
  List<dynamic> favouriteProducts = [];
  String? currentProductsJSON =
      await (NyStorage.read(SharedKey.wishlistProducts));
  if (currentProductsJSON != null) {
    favouriteProducts = (jsonDecode(currentProductsJSON)).toList();
  }
  return favouriteProducts;
}

Future<bool> hasAddedWishlistProduct(int? productId) async {
  List<dynamic> favouriteProducts = await getWishlistProducts();
  List<int> productIds =
      favouriteProducts.map((e) => e['id']).cast<int>().toList();
  if (productIds.isEmpty) {
    return false;
  }
  return productIds.contains(productId);
}

saveWishlistProduct({required MyProduct? product}) async {
  List<dynamic> products = await getWishlistProducts();
  if (products.any((wishListProduct) => wishListProduct['id'] == product?.id) ==
      false) {
    products.add({"id": product!.id});
  }
  String json = jsonEncode(products.map((i) => {"id": i['id']}).toList());
  await NyStorage.save(SharedKey.wishlistProducts, json);
}

removeWishlistProduct({required MyProduct? product}) async {
  List<dynamic> products = await getWishlistProducts();
  products.removeWhere((element) => element['id'] == product?.id);

  String json = jsonEncode(products.map((i) => {"id": i['id']}).toList());
  await NyStorage.save(SharedKey.wishlistProducts, json);
}

/// Extract billing details from WooCommerce customer
Future<BillingDetails> billingDetailsFromWooCustomer(WooCustomer customer) async {
  BillingDetails billingDetails = BillingDetails();

  // Create billing address from customer billing info
  CustomerAddress billingAddress = CustomerAddress();
  billingAddress.firstName = customer.billing?.firstName;
  billingAddress.lastName = customer.billing?.lastName;
  billingAddress.addressLine = customer.billing?.address1;
  billingAddress.city = customer.billing?.city;
  billingAddress.postalCode = customer.billing?.postcode;
  billingAddress.emailAddress = customer.billing?.email ?? customer.email;
  billingAddress.phoneNumber = customer.billing?.phone;

  // Set country information
  if (customer.billing?.country != null) {
    DefaultShipping? defaultShipping = await findCountryMetaForShipping(customer.billing!.country!);
    if (defaultShipping != null) {
      billingAddress.customerCountry = CustomerCountry.fromDefaultShipping(
        defaultShipping: defaultShipping
      );
    }
  }

  billingDetails.billingAddress = billingAddress;

  // Create shipping address from customer shipping info (if different)
  if (customer.shipping != null &&
      (customer.shipping!.firstName?.isNotEmpty == true ||
       customer.shipping!.address1?.isNotEmpty == true)) {
    CustomerAddress shippingAddress = CustomerAddress();
    shippingAddress.firstName = customer.shipping?.firstName;
    shippingAddress.lastName = customer.shipping?.lastName;
    shippingAddress.addressLine = customer.shipping?.address1;
    shippingAddress.city = customer.shipping?.city;
    shippingAddress.postalCode = customer.shipping?.postcode;

    // Set country information for shipping
    if (customer.shipping?.country != null) {
      DefaultShipping? defaultShipping = await findCountryMetaForShipping(customer.shipping!.country!);
      if (defaultShipping != null) {
        shippingAddress.customerCountry = CustomerCountry.fromDefaultShipping(
          defaultShipping: defaultShipping
        );
      }
    }

    billingDetails.shippingAddress = shippingAddress;
  } else {
    // Use billing address as shipping address if no separate shipping address
    billingDetails.shippingAddress = billingAddress;
  }

  return billingDetails;
}

/// Legacy function for backward compatibility
Future<BillingDetails> billingDetailsFromWpUserInfoResponse(
    wpUserInfoResponse) async {
  // Legacy function - for backward compatibility only
  // New code should use billingDetailsFromWooCustomer instead
  BillingDetails billingDetails = BillingDetails();
  return billingDetails;
}

/// Check if the [MyProduct] is new.
bool isProductNew(dynamic product) {
  // Handle both WooProduct and MyProduct
  DateTime? dateCreated;

  if (product == null) return false;

  // Check if it's MyProduct
  if (product.runtimeType.toString().contains('MyProduct')) {
    dateCreated = product.dateCreated;
  } else {
    // Assume it's WooProduct or similar
    try {
      dateCreated = product.dateCreated;
    } catch (e) {
      return false;
    }
  }

  if (dateCreated == null) return false;

  try {
    return dateCreated.isBetween(
            DateTime.now().subtract(Duration(days: 2)), DateTime.now()) ??
        false;
  } on Exception catch (e) {
    NyLogger.error(e.toString());
  }
  return false;
}

bool shouldEncrypt() {
  String? encryptKey = getEnv('ENCRYPT_KEY', defaultValue: "");
  if (encryptKey == null || encryptKey == "") {
    return false;
  }
  String? encryptSecret = getEnv('ENCRYPT_KEY', defaultValue: "");
  if (encryptSecret == null || encryptSecret == "") {
    return false;
  }
  return true;
}

bool isFirebaseEnabled() {
  bool? firebaseFcmIsEnabled =
      AppHelper.instance.appConfig?.firebaseFcmIsEnabled;
  firebaseFcmIsEnabled ??= getEnv('FCM_ENABLED', defaultValue: false);

  return firebaseFcmIsEnabled == true;
}

/// Helper function to get correct image asset path
/// This function ensures local assets are not prefixed with APP_URL
String getImageAsset(String assetPath) {
  // If it's already a network URL, return as-is
  if (assetPath.startsWith('http://') || assetPath.startsWith('https://')) {
    return assetPath;
  }

  // For local assets, ensure they start with the correct path
  // Remove any null prefixes or incorrect APP_URL prefixes
  String cleanPath = assetPath;

  // Remove any "null/" prefix that might be added incorrectly
  if (cleanPath.startsWith('null/')) {
    cleanPath = cleanPath.substring(5);
  }

  // Ensure the path starts with the correct asset directory
  if (!cleanPath.startsWith('public/')) {
    cleanPath = 'public/$cleanPath';
  }

  return cleanPath;
}

class NotificationItem extends Model {
  String? id;
  String? title;
  String? message;
  bool? hasRead;
  String? type;
  Map<String, dynamic>? meta;
  String? createdAt;

  NotificationItem(
      {this.title,
      this.message,
      this.id,
      this.type,
      this.meta,
      this.createdAt,
      this.hasRead = false});

  NotificationItem.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        title = json['title'],
        type = json['type'],
        meta = json['meta'],
        message = json['message'],
        hasRead = json['has_read'],
        createdAt = json['created_at'];

  fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    type = json['type'];
    meta = json['meta'];
    message = json['message'];
    hasRead = json['has_read'];
    createdAt = json['created_at'];
  }

  @override
  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'type': type,
        'meta': meta,
        'message': message,
        'has_read': hasRead,
        "created_at": createdAt
      };
}

class NyNotification {
  static final String _storageKey = "app_notifications";

  static String storageKey() => _storageKey;

  /// Add a notification
  static addNotification(String title, String message,
      {String? id, Map<String, dynamic>? meta}) async {
    NotificationItem notificationItem = NotificationItem.fromJson({
      "id": id,
      "title": title,
      "message": message,
      "meta": meta,
      "has_read": false,
      "created_at": DateTime.now().toDateTimeString()
    });
    await NyStorage.addToCollection<NotificationItem>(storageKey(),
        item: notificationItem,
        allowDuplicates: false,
        modelDecoders: {
          NotificationItem: (data) => NotificationItem.fromJson(data),
        });
  }

  /// Get all notifications
  static Future<List<NotificationItem>> allNotifications() async {
    List<NotificationItem> notifications =
        await NyStorage.readCollection<NotificationItem>(storageKey(),
            modelDecoders: {
          NotificationItem: (data) => NotificationItem.fromJson(data),
        });
    // Filter notifications by current user if user is logged in
    String? userId = await getCurrentUserId();
    if (userId != null) {
      notifications.removeWhere((notification) {
        if (notification.meta != null &&
            notification.meta!.containsKey('user_id')) {
          if (notification.meta?['user_id'] != userId) {
            return true;
          }
        }
        // If no user_id specified in notification, show to all users
        return false;
      });
      print('✅ Filtered notifications for user $userId: ${notifications.length} notifications');
    } else {
      print('✅ No user logged in, showing all notifications: ${notifications.length} notifications');
    }

    await NyStorage.saveCollection(storageKey(), notifications);

    return notifications;
  }

  /// Get all notifications not read
  static Future<List<NotificationItem>> allNotificationsNotRead() async {
    List<NotificationItem> notifications = await allNotifications();
    return notifications.where((element) => element.hasRead == false).toList();
  }

  /// Mark notification as read by index
  static markReadByIndex(int index) async {
    await NyStorage.updateCollectionByIndex(index, (item) {
      item as NotificationItem;
      item.hasRead = true;
      return item;
    }, key: storageKey());
  }

  /// Mark all notifications as read
  static markReadAll() async {
    List<NotificationItem> notifications = await allNotifications();
    for (var i = 0; i < notifications.length; i++) {
      await markReadByIndex(i);
    }
  }

  /// Clear all notifications
  static clearAllNotifications() async {
    await NyStorage.deleteCollection(storageKey());
  }

  /// Render notifications
  static Widget renderNotifications(
      Widget Function(List<NotificationItem> notificationItems) child,
      {Widget? loading}) {
    return NyFutureBuilder(
        future: allNotifications(),
        child: (context, data) {
          if (data == null) {
            return SizedBox.shrink();
          }
          return child(data);
        },
        loadingStyle: LoadingStyle.normal(child: loading));
  }

  /// Render list of notifications
  static Widget renderListNotifications(
      Widget Function(NotificationItem notificationItems) child,
      {Widget? loading}) {
    return NyFutureBuilder(
        future: allNotifications(),
        child: (context, data) {
          if (data == null) {
            return SizedBox.shrink();
          }
          return NyListView(child: (context, item) {
            item as NotificationItem;
            return child(item);
          }, data: () async {
            return data.reversed.toList();
          });
        },
        loadingStyle: LoadingStyle.normal(child: loading));
  }

  /// Render list of notifications
  static Widget renderListNotificationsWithSeparator(
      Widget Function(NotificationItem notificationItems) child,
      {Widget? loading}) {
    return NyFutureBuilder(
        future: allNotifications(),
        child: (context, data) {
          if (data == null) {
            return SizedBox.shrink();
          }
          return NyListView.separated(
            child: (context, item) {
              item as NotificationItem;
              return child(item);
            },
            data: () async {
              return data.reversed.toList();
            },
            separatorBuilder: (context, index) {
              return Divider(
                color: Colors.grey.shade100,
              );
            },
          );
        },
        loadingStyle: LoadingStyle.normal(child: loading));
  }
}

Future<bool> canSeeRemoteMessage(RemoteMessage message) async {
  if (!message.data.containsKey('user_id')) {
    return true;
  }

  // Check if user is authenticated and matches the message target user
  String userId = message.data['user_id'];

  // Check if user is logged in
  if (!(await isUserLoggedIn())) {
    print('❌ User not logged in, rejecting remote message for user $userId');
    return false;
  }

  // Check if current user matches the target user
  String? currentUserId = await getCurrentUserId();
  bool userMatches = currentUserId == userId;

  print(userMatches
    ? '✅ Remote message allowed for user $userId'
    : '❌ Remote message rejected: current user $currentUserId != target user $userId');

  return userMatches;
}
