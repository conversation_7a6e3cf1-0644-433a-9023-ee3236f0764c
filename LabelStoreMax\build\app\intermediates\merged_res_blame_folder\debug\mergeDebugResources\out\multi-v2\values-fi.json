{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-111:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,378,515,648,797,918,1048,1148,1291,1429", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "159,252,373,510,643,792,913,1043,1143,1286,1424,1541"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9646,10027,11089,11210,11347,11480,11629,11750,11880,11980,12123,12261", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "9750,10115,11205,11342,11475,11624,11745,11875,11975,12118,12256,12373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4822,10221,10608", "endColumns": "79,78,78", "endOffsets": "4897,10295,10682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9825,10780,10881,10990", "endColumns": "102,100,108,98", "endOffsets": "9923,10876,10985,11084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "12378,49670", "endColumns": "60,79", "endOffsets": "12434,49745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20099,20338,20601,20754,20846,20914,21418,21485,21623,21685,21755,21830,21890,21952,22027,22150,22223,22462,23384,23452,24466,24618,24696,28876,28979,30261,31102,31154,31289,31971,32040,32850,32949,33671,33764", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "20158,20409,20666,20841,20909,20970,21480,21549,21680,21750,21825,21885,21947,22022,22086,22218,22282,22537,23447,23536,24536,24691,24775,28974,29058,30305,31149,31229,31346,32035,32107,32944,33031,33759,33849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8468", "endColumns": "149", "endOffsets": "8613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9755,10516,17496,17787,48878,49498,49588", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "9820,10603,17569,17917,49042,49583,49665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,348,494,606,690,777,853,943,1041,1159,1276,1373,1470,1583,1716,1798,1878,2052,2147,2260,2374,2492", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "212,343,489,601,685,772,848,938,1036,1154,1271,1368,1465,1578,1711,1793,1873,2047,2142,2255,2369,2487,2623"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4902,5064,5195,5341,5453,5537,5624,5700,5790,5888,6006,6123,6220,6317,6430,6563,6645,6725,6899,6994,7107,7221,7339", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "5059,5190,5336,5448,5532,5619,5695,5785,5883,6001,6118,6215,6312,6425,6558,6640,6720,6894,6989,7102,7216,7334,7470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "32315", "endColumns": "88", "endOffsets": "32399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3217,3293,3367,3450,3539,4355,4451,4559,10389,10451,10687,12439,12684,12749,12837,12902,12968,13026,13097,13163,13217,13327,13387,13451,13505,13578,13694,13778,13854,13945,14026,14107,14240,14325,14410,14543,14633,14707,14759,14810,14876,14953,15035,15106,15180,15254,15333,15410,15482,15589,15678,15754,15845,15940,16014,16087,16181,16235,16309,16381,16467,16553,16615,16679,16742,16813,16914,17017,17112,17212,17268,17708,18166,18252,18403", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3288,3362,3445,3534,3616,4446,4554,4638,10446,10511,10775,12509,12744,12832,12897,12963,13021,13092,13158,13212,13322,13382,13446,13500,13573,13689,13773,13849,13940,14021,14102,14235,14320,14405,14538,14628,14702,14754,14805,14871,14948,15030,15101,15175,15249,15328,15405,15477,15584,15673,15749,15840,15935,16009,16082,16176,16230,16304,16376,16462,16548,16610,16674,16737,16808,16909,17012,17107,17207,17263,17318,17782,18247,18326,18473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2988,3100", "endColumns": "111,116", "endOffsets": "3095,3212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3621,3717,3819,3917,4022,4127,4239,18478", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3712,3814,3912,4017,4122,4234,4350,18574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4643,4737,9928,10120,10300,12514,12591,17323,17414,17574,17640,17922,18003,18331,49047,49124,49196", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4732,4817,10022,10216,10384,12586,12679,17409,17491,17635,17703,17998,18080,18398,49119,49191,49313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "49318,49408", "endColumns": "89,89", "endOffsets": "49403,49493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,273,367,464,543,621,737,831,920,1013,1148,1221,1330,1401,1566,1759,1906,1994,2085,2180,2275,2467,2567,2664,2896,2989,3112,3302,3511,3605,3715,3888,3976,4036,4101,4179,4277,4384,4462,4554,4652,4751,4956,5025,5094,5164,5279,5361,5445,5510,5590,5661,5736,5860,5949,6057,6149,6223,6304,6372,6453,6515,6613,6726,6831,6962,7028,7116,7209,7336,7410,7488,7588,7647,7711,7805,7920,8074,8322,8591,8683,8758,8846,8914,9017,9130,9216,9295,9369,9459,9535,9626,9742,9869,9934,10012,10069,10211,10277,10334,10412,10480,10558,10639,10770,10890,10997,11084,11164,11249,11318", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "119,198,268,362,459,538,616,732,826,915,1008,1143,1216,1325,1396,1561,1754,1901,1989,2080,2175,2270,2462,2562,2659,2891,2984,3107,3297,3506,3600,3710,3883,3971,4031,4096,4174,4272,4379,4457,4549,4647,4746,4951,5020,5089,5159,5274,5356,5440,5505,5585,5656,5731,5855,5944,6052,6144,6218,6299,6367,6448,6510,6608,6721,6826,6957,7023,7111,7204,7331,7405,7483,7583,7642,7706,7800,7915,8069,8317,8586,8678,8753,8841,8909,9012,9125,9211,9290,9364,9454,9530,9621,9737,9864,9929,10007,10064,10206,10272,10329,10407,10475,10553,10634,10765,10885,10992,11079,11159,11244,11313,11460"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19604,19673,19752,25357,27058,27155,27234,29119,30310,30451,30540,33036,34799,34940,35171,35478,35643,35836,36132,36318,36511,36606,36701,36893,36993,37090,37322,37415,37538,37728,37937,38126,38236,38409,38497,38557,38622,38700,38798,38905,38983,39075,39173,39272,39477,39546,39615,39685,39800,39882,39966,40031,40111,40182,40257,40813,40902,41010,41102,41176,41257,41325,41406,41468,41566,41679,41784,41915,41981,42069,42162,42289,42547,42863,44398,44457,44521,44615,44730,44884,45132,45401,45493,46101,46189,46257,46360,46473,46559,46638,46712,46802,46878,46969,47085,47212,47277,47425,47482,47624,47690,47747,47825,47893,47971,48052,48183,48303,48410,48497,48577,48662,48731", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "19668,19747,19817,25446,27150,27229,27307,29230,30399,30535,30628,33166,34867,35044,35237,35638,35831,35978,36215,36404,36601,36696,36888,36988,37085,37317,37410,37533,37723,37932,38026,38231,38404,38492,38552,38617,38695,38793,38900,38978,39070,39168,39267,39472,39541,39610,39680,39795,39877,39961,40026,40106,40177,40252,40376,40897,41005,41097,41171,41252,41320,41401,41463,41561,41674,41779,41910,41976,42064,42157,42284,42358,42620,42958,44452,44516,44610,44725,44879,45127,45396,45488,45563,46184,46252,46355,46468,46554,46633,46707,46797,46873,46964,47080,47207,47272,47350,47477,47619,47685,47742,47820,47888,47966,48047,48178,48298,48405,48492,48572,48657,48726,48873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18579,18663,18763,18829,18899,18964,19039", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "18658,18758,18824,18894,18959,19034,19103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,18085", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,18161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7475,7586,7739,7870,7976,8119,8245,8361,8618,8759,8865,9014,9140,9288,9427,9493,9563", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "7581,7734,7865,7971,8114,8240,8356,8463,8754,8860,9009,9135,9283,9422,9488,9558,9641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3245,3312,3377,3974,4543,4616,4727,4822,4877,4977,5073,5143,5242,5336,5393,5472,5522,5600,5705,5778,5857,5924,5990,6037,6111,6199,6280,6327,6375,6447,6505,6572,6728,6879,6995,7068,7143,7223,7315,7395,7477,7553,7642,7719,7838,7922,8012,8067,8201,8250,8306,8375,8440,8509,8574,8640,8729,8799,8849", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3240,3307,3372,3969,4538,4611,4722,4817,4872,4972,5068,5138,5237,5331,5388,5467,5517,5595,5700,5773,5852,5919,5985,6032,6106,6194,6275,6322,6370,6442,6500,6567,6723,6874,6990,7063,7138,7218,7310,7390,7472,7548,7637,7714,7833,7917,8007,8062,8196,8245,8301,8370,8435,8504,8569,8635,8724,8794,8844,8914"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19108,19179,19259,19321,19394,19476,19537,19822,19874,19952,20025,20239,20414,20513,20671,21041,21333,22363,22615,22711,22781,22942,23032,23120,23296,23611,23704,23781,23881,23954,24044,24135,24219,24298,24398,24541,24780,24890,25637,25704,25769,27312,27881,27954,28065,28160,28215,28315,28411,28481,28580,28674,28731,29299,29349,29427,29698,29771,29850,29917,30404,30764,30838,30926,31007,31054,31351,31423,31481,31548,31704,31855,32167,32240,33171,33251,33418,33498,33854,33930,34093,34680,35242,35326,42625,42680,42814,42963,43523,45568,45633,45702,45767,45833,45922,45992,47355", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "19174,19254,19316,19389,19471,19532,19599,19869,19947,20020,20094,20333,20508,20596,20749,21125,21413,22457,22706,22776,22869,23027,23115,23226,23379,23699,23776,23876,23949,24039,24130,24214,24293,24393,24461,24613,24885,24982,25699,25764,26361,27876,27949,28060,28155,28210,28310,28406,28476,28575,28669,28726,28805,29344,29422,29527,29766,29845,29912,29978,30446,30833,30921,31002,31049,31097,31418,31476,31543,31699,31850,31966,32235,32310,33246,33338,33493,33575,33925,34014,34165,34794,35321,35411,42675,42809,42858,43014,43587,45628,45697,45762,45828,45917,45987,46037,47420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20163,20975,21130,21191,21271,21554,22091,22287,22542,22874,23231,23541", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "20234,21036,21186,21266,21328,21618,22145,22358,22610,22937,23291,23606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,336,425,611,660,726,823,893,1134,1204,1303,1369,1425,1489,1564,1655,1933,2001,2064,2119,2174,2248,2366,2474,2533,2620,2695,2786,2860,2960,3214,3293,3370,3438,3498,3560,3622,3692,3771,3869,3971,4066,4166,4259,4334,4413,4498,4682,4877,4993,5126,5186,5829,5930,5992", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "171,331,420,606,655,721,818,888,1129,1199,1298,1364,1420,1484,1559,1650,1928,1996,2059,2114,2169,2243,2361,2469,2528,2615,2690,2781,2855,2955,3209,3288,3365,3433,3493,3555,3617,3687,3766,3864,3966,4061,4161,4254,4329,4408,4493,4677,4872,4988,5121,5181,5824,5925,5987,6046"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24987,25108,25268,25451,26366,26415,26481,26578,26648,26889,26959,28810,29063,29235,29532,29607,29983,30633,30701,31234,32112,32404,32478,32596,32704,32763,33343,33580,34019,34170,34270,34524,34603,34872,35049,35109,35416,35983,36053,36220,36409,38031,40381,40481,40574,40649,40728,42363,43019,43214,43330,43463,43592,44235,44336,46042", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "25103,25263,25352,25632,26410,26476,26573,26643,26884,26954,27053,28871,29114,29294,29602,29693,30256,30696,30759,31284,32162,32473,32591,32699,32758,32845,33413,33666,34088,34265,34519,34598,34675,34935,35104,35166,35473,36048,36127,36313,36506,38121,40476,40569,40644,40723,40808,42542,43209,43325,43458,43518,44230,44331,44393,46096"}}]}]}